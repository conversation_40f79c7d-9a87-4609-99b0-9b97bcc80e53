#!/bin/bash
# Script de vérification des prérequis

echo "🔍 Vérification des prérequis pour Ma Compta Perso"
echo "================================================"

# Vérifier Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "✅ Node.js: $NODE_VERSION"
    
    # Vérifier la version
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_MAJOR" -ge 16 ]; then
        echo "   ✅ Version OK (>= 16)"
    else
        echo "   ❌ Version trop ancienne (< 16)"
    fi
else
    echo "❌ Node.js: Non installé"
fi

# Vérifier npm
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo "✅ npm: $NPM_VERSION"
else
    echo "❌ npm: Non installé"
fi

# Vérifier Git
if command -v git &> /dev/null; then
    GIT_VERSION=$(git --version)
    echo "✅ Git: $GIT_VERSION"
else
    echo "❌ Git: Non installé"
fi

# Vérifier l'accès au repository
echo ""
echo "🌐 Test d'accès au repository..."
if git ls-remote https://github.com/ppaperso/MaComptaPerso.git &> /dev/null; then
    echo "✅ Accès au repository: OK"
else
    echo "❌ Accès au repository: Échec"
fi

# Vérifier l'espace disque
echo ""
echo "💾 Espace disque disponible dans le home:"
df -h ~ | tail -1

echo ""
echo "🎯 Résumé:"
if command -v node &> /dev/null && command -v npm &> /dev/null && command -v git &> /dev/null; then
    echo "✅ Tous les prérequis sont installés !"
    echo "🚀 Vous pouvez procéder à l'installation"
else
    echo "❌ Certains prérequis manquent"
    echo "💡 Demandez à l'admin d'installer les packages manquants"
fi
