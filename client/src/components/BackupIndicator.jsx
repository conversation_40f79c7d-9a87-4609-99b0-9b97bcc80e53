import React, { useState, useEffect } from 'react'

export default function BackupIndicator({ lastBackup, apiBase, authToken }) {
  const [showModal, setShowModal] = useState(false)
  const [backupHistory, setBackupHistory] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')



  // Déterminer l'état de l'indicateur
  const getIndicatorState = () => {
    if (!lastBackup) return 'unknown'
    if (lastBackup.status === 'success') return 'success'
    if (lastBackup.status === 'error') return 'error'
    return 'unknown'
  }

  // Obtenir le style de l'indicateur selon l'état
  const getIndicatorStyle = () => {
    const state = getIndicatorState()
    const baseStyle = {
      fontSize: '24px',
      fontWeight: 'bold',
      cursor: 'pointer',
      padding: '8px 12px',
      borderRadius: '50%',
      border: '2px solid',
      background: 'white',
      transition: 'all 0.3s ease',
      userSelect: 'none',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '40px',
      height: '40px'
    }

    switch (state) {
      case 'success':
        return {
          ...baseStyle,
          color: '#22c55e',
          borderColor: '#22c55e',
          boxShadow: '0 0 10px rgba(34, 197, 94, 0.3)'
        }
      case 'error':
        return {
          ...baseStyle,
          color: '#ef4444',
          borderColor: '#ef4444',
          boxShadow: '0 0 10px rgba(239, 68, 68, 0.3)'
        }
      default:
        return {
          ...baseStyle,
          color: '#6b7280',
          borderColor: '#6b7280',
          boxShadow: '0 0 5px rgba(107, 114, 128, 0.2)'
        }
    }
  }

  // Charger l'historique des sauvegardes
  const loadBackupHistory = async () => {
    setLoading(true)
    setError('')
    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/backup/history`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setBackupHistory(data)
      } else {
        setError('Erreur lors du chargement de l\'historique')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  // Supprimer un fichier de sauvegarde
  const deleteBackupFile = async (backupId, fileName) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer la sauvegarde "${fileName}" ?`)) {
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/backup/${backupId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        // Recharger l'historique
        await loadBackupHistory()
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de la suppression')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    }
  }

  // Formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Formater la taille du fichier
  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A'
    const kb = bytes / 1024
    if (kb < 1024) return `${kb.toFixed(1)} KB`
    const mb = kb / 1024
    return `${mb.toFixed(1)} MB`
  }

  // Ouvrir la modal et charger l'historique
  const handleClick = () => {
    setShowModal(true)
    loadBackupHistory()
  }

  // Fermer la modal
  const handleCloseModal = () => {
    setShowModal(false)
  }

  // Obtenir le titre de l'indicateur
  const getTitle = () => {
    const state = getIndicatorState()
    switch (state) {
      case 'success':
        return 'Sauvegarde OK - Cliquez pour voir l\'historique'
      case 'error':
        return 'Erreur de sauvegarde - Cliquez pour voir les détails'
      default:
        return 'État de sauvegarde inconnu - Cliquez pour voir l\'historique'
    }
  }

  return (
    <>
      {/* Indicateur de sauvegarde */}
      <div
        style={getIndicatorStyle()}
        onClick={handleClick}
        title={getTitle()}
      >
        S
      </div>

      {/* Modal d'historique */}
      {showModal && (
        <div className="profile-overlay" onClick={handleCloseModal}>
          <div className="profile-modal" onClick={e => e.stopPropagation()}>
            <div className="profile-header">
              <h2>📁 Historique des sauvegardes</h2>
              <button onClick={handleCloseModal} className="close-button">✕</button>
            </div>

            <div className="profile-content">
              {/* Informations sur la dernière sauvegarde */}
              {lastBackup && (
                <div className="backup-summary">
                  <h4>📊 Dernière sauvegarde</h4>
                  <div className="backup-info">
                    <div className="info-row">
                      <span className="label">État :</span>
                      <span className={`status ${lastBackup.status}`}>
                        {lastBackup.status === 'success' ? '✅ Réussie' : '❌ Échec'}
                      </span>
                    </div>
                    <div className="info-row">
                      <span className="label">Date :</span>
                      <span>{formatDate(lastBackup.backup_date)}</span>
                    </div>
                    {lastBackup.status === 'success' && (
                      <>
                        <div className="info-row">
                          <span className="label">Transactions :</span>
                          <span>{lastBackup.transactions_count}</span>
                        </div>
                        <div className="info-row">
                          <span className="label">Catégories :</span>
                          <span>{lastBackup.categories_count}</span>
                        </div>
                        {lastBackup.backup_file_size && (
                          <div className="info-row">
                            <span className="label">Taille :</span>
                            <span>{formatFileSize(lastBackup.backup_file_size)}</span>
                          </div>
                        )}
                      </>
                    )}
                    {lastBackup.status === 'error' && lastBackup.error_message && (
                      <div className="info-row">
                        <span className="label">Erreur :</span>
                        <span className="error-text">{lastBackup.error_message}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Historique complet */}
              <div className="backup-history">
                <h4>📋 Historique complet</h4>
                
                {error && (
                  <div className="error-message">
                    {error}
                  </div>
                )}

                {loading ? (
                  <div className="loading">⏳ Chargement...</div>
                ) : backupHistory.length === 0 ? (
                  <div className="no-data">Aucune sauvegarde trouvée</div>
                ) : (
                  <div className="history-list">
                    {backupHistory.map(backup => (
                      <div key={backup.id} className="history-item">
                        <div className="backup-details">
                          <div className="backup-date">
                            📅 {formatDate(backup.backup_date)}
                          </div>
                          <div className="backup-status">
                            {backup.status === 'success' ? '✅' : '❌'} {backup.status}
                          </div>
                          {backup.status === 'success' && (
                            <div className="backup-stats">
                              {backup.transactions_count} transactions, {backup.categories_count} catégories
                              {backup.backup_file_size && ` • ${formatFileSize(backup.backup_file_size)}`}
                            </div>
                          )}
                          {backup.status === 'error' && backup.error_message && (
                            <div className="backup-error">
                              ⚠️ {backup.error_message}
                            </div>
                          )}
                        </div>
                        {backup.backup_file_path && (
                          <div className="backup-actions">
                            <button
                              className="delete-button"
                              onClick={() => deleteBackupFile(backup.id, backup.backup_file_path)}
                              title="Supprimer cette sauvegarde"
                            >
                              🗑️
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
