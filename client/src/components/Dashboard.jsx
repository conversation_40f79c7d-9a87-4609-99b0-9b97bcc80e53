import React from 'react'
import BalanceCard from './BalanceCard'
import StatsCard from './StatsCard'

export default function Dashboard({ balance, transactions }) {
  const today = new Date()
  today.setHours(23, 59, 59, 999) // Fin de la journée

  // Séparer les transactions passées/présentes des futures
  const pastTransactions = transactions.filter(t => new Date(t.date_iso) <= today)
  const futureTransactions = transactions.filter(t => new Date(t.date_iso) > today)

  const recentTransactions = pastTransactions.slice(0, 5)
  const upcomingTransactions = futureTransactions
    .sort((a, b) => new Date(a.date_iso) - new Date(b.date_iso)) // Trier par date croissante
    .slice(0, 5)


  
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }



  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>📊 Tableau de bord</h2>
        <p>Vue d'ensemble de vos finances personnelles</p>
      </div>

      <BalanceCard balance={balance} />
      <StatsCard transactions={transactions} />


      {recentTransactions.length > 0 && (
        <div className="card">
          <h3>🕒 Transactions récentes</h3>
          <div className="recent-transactions">
            {recentTransactions.map(transaction => (
              <div key={transaction.id} className="recent-transaction-item">
                <div className="transaction-info">
                  <div className="transaction-type">
                    <span className={`badge ${transaction.type === 'in' ? 'income' : 'expense'}`}>
                      {transaction.type === 'in' ? '💰' : '💸'}
                    </span>
                  </div>
                  <div className="transaction-details">
                    <div className="transaction-description">
                      {transaction.description || 'Transaction sans description'}
                    </div>
                    <div className="transaction-category">
                      {transaction.category_name || 'Sans catégorie'} • {formatDate(transaction.date_iso)}
                    </div>
                  </div>
                </div>
                <div className={`transaction-amount ${transaction.type === 'in' ? 'positive' : 'negative'}`}>
                  {transaction.type === 'in' ? '+' : '-'}{formatCurrency(transaction.amount)}
                </div>
              </div>
            ))}
          </div>
          <div className="dashboard-action">
            <p style={{ textAlign: 'center', marginTop: '16px', color: '#666' }}>
              Consultez l'historique complet pour voir toutes vos transactions
            </p>
          </div>
        </div>
      )}

      {upcomingTransactions.length > 0 && (
        <div className="card">
          <h3>🔮 Transactions à venir</h3>
          <div className="upcoming-transactions">
            {upcomingTransactions.map(transaction => (
              <div key={transaction.id} className="recent-transaction-item" style={{ opacity: 0.8 }}>
                <div className="transaction-info">
                  <div className="transaction-type">
                    <span className={`badge ${transaction.type === 'in' ? 'income' : 'expense'}`} style={{ opacity: 0.7 }}>
                      {transaction.type === 'in' ? '💰' : '💸'}
                    </span>
                  </div>
                  <div className="transaction-details">
                    <div className="transaction-description">
                      {transaction.description || 'Transaction sans description'}
                    </div>
                    <div className="transaction-category">
                      {transaction.category_name || 'Sans catégorie'} • {formatDate(transaction.date_iso)}
                    </div>
                  </div>
                </div>
                <div className={`transaction-amount ${transaction.type === 'in' ? 'positive' : 'negative'}`} style={{ opacity: 0.7 }}>
                  {transaction.type === 'in' ? '+' : '-'}{formatCurrency(transaction.amount)}
                </div>
              </div>
            ))}
          </div>
          <div className="dashboard-action">
            <p style={{ textAlign: 'center', marginTop: '16px', color: '#666', fontSize: '0.9rem' }}>
              💡 Ces transactions sont programmées pour le futur et ne sont pas incluses dans votre solde actuel
            </p>
          </div>
        </div>
      )}

      {transactions.length === 0 && (
        <div className="card empty-state">
          <div className="empty-state-content">
            <div className="empty-state-icon">📝</div>
            <h3>Commencez votre suivi financier</h3>
            <p>Ajoutez votre première transaction pour voir apparaître vos statistiques ici.</p>
          </div>
        </div>
      )}
    </div>
  )
}
