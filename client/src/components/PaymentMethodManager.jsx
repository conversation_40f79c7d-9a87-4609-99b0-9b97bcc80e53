import React, { useState, useEffect } from 'react'

export default function PaymentMethodManager({ apiBase, authToken, onClose }) {
  const [paymentMethods, setPaymentMethods] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [editingMethod, setEditingMethod] = useState(null)
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    requires_check_number: false
  })

  useEffect(() => {
    loadPaymentMethods()
  }, [])

  const loadPaymentMethods = async () => {
    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/payment-methods`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setPaymentMethods(data)
      } else {
        setError('Erreur lors du chargement des modes de paiement')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    if (!formData.name.trim()) {
      setError('Le nom du mode de paiement est requis')
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      const url = editingMethod 
        ? `${apiBase}/payment-methods/${editingMethod.id}`
        : `${apiBase}/payment-methods`
      
      const response = await fetch(url, {
        method: editingMethod ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        await loadPaymentMethods()
        resetForm()
        setError('')
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de l\'enregistrement')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    }
  }

  const handleEdit = (method) => {
    setEditingMethod(method)
    setFormData({
      name: method.name,
      requires_check_number: method.requires_check_number === 1
    })
    setShowForm(true)
  }

  const handleDelete = async (id) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce mode de paiement ?')) {
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/payment-methods/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        await loadPaymentMethods()
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de la suppression')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    }
  }

  const resetForm = () => {
    setFormData({ name: '', requires_check_number: false })
    setEditingMethod(null)
    setShowForm(false)
  }

  const createDefaultPaymentMethods = async () => {
    const defaultMethods = [
      { name: 'Chèque', requires_check_number: true },
      { name: 'Virement', requires_check_number: false },
      { name: 'CB Patrick', requires_check_number: false },
      { name: 'CB Isabelle', requires_check_number: false },
      { name: 'Espèces', requires_check_number: false }
    ]

    try {
      const token = authToken || localStorage.getItem('authToken')
      
      for (const method of defaultMethods) {
        await fetch(`${apiBase}/payment-methods`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(method)
        })
      }
      
      await loadPaymentMethods()
    } catch (err) {
      setError('Erreur lors de la création des modes de paiement par défaut')
    }
  }

  if (loading) {
    return (
      <div className="card">
        <div className="loading">Chargement...</div>
      </div>
    )
  }

  return (
    <div className="payment-methods-manager">
      <div className="card">
        <div className="card-header">
          <h2>💳 Gestion des modes de paiement</h2>
          <button onClick={onClose} className="close-btn">✕</button>
        </div>

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <div className="payment-methods-actions">
          <button 
            onClick={() => setShowForm(true)} 
            className="success"
          >
            ➕ Nouveau mode de paiement
          </button>
          
          {paymentMethods.length === 0 && (
            <button 
              onClick={createDefaultPaymentMethods}
              className="primary"
            >
              🔧 Créer les modes par défaut
            </button>
          )}
        </div>

        {showForm && (
          <form onSubmit={handleSubmit} className="payment-method-form">
            <h3>{editingMethod ? '✏️ Modifier' : '➕ Ajouter'} un mode de paiement</h3>
            
            <div className="row">
              <label htmlFor="method-name">Nom du mode de paiement *</label>
              <input
                id="method-name"
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="ex: Chèque, CB Patrick..."
                required
              />
            </div>

            <div className="row">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={formData.requires_check_number}
                  onChange={(e) => setFormData({...formData, requires_check_number: e.target.checked})}
                />
                Nécessite un numéro de chèque
              </label>
            </div>

            <div className="actions">
              <button type="submit" className="success">
                {editingMethod ? '✅ Modifier' : '✅ Ajouter'}
              </button>
              <button type="button" onClick={resetForm} className="secondary">
                ❌ Annuler
              </button>
            </div>
          </form>
        )}

        <div className="payment-methods-list">
          <h3>📋 Modes de paiement existants</h3>
          
          {paymentMethods.length === 0 ? (
            <p className="no-data">Aucun mode de paiement configuré</p>
          ) : (
            <div className="methods-grid">
              {paymentMethods.map(method => (
                <div key={method.id} className="method-card">
                  <div className="method-info">
                    <h4>{method.name}</h4>
                    {method.requires_check_number === 1 && (
                      <span className="check-badge">📝 Numéro requis</span>
                    )}
                  </div>
                  <div className="method-actions">
                    <button 
                      onClick={() => handleEdit(method)}
                      className="edit-btn"
                      title="Modifier"
                    >
                      ✏️
                    </button>
                    <button 
                      onClick={() => handleDelete(method.id)}
                      className="delete-btn"
                      title="Supprimer"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
