import React, { useState, useEffect } from 'react'

export default function RecipientManager({ apiBase, authToken }) {
  const [recipients, setRecipients] = useState([])
  const [newRecipientName, setNewRecipientName] = useState('')
  const [editingRecipient, setEditingRecipient] = useState(null)
  const [editName, setEditName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  useEffect(() => {
    loadRecipients()
  }, [])

  const loadRecipients = async () => {
    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/recipients`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setRecipients(data)
      } else {
        setError('Erreur lors du chargement des destinataires')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    }
  }

  const addRecipient = async (e) => {
    e.preventDefault()
    if (!newRecipientName.trim()) return

    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/recipients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ name: newRecipientName.trim() })
      })

      if (response.ok) {
        setNewRecipientName('')
        setSuccess('Destinataire ajouté avec succès !')
        await loadRecipients()
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de l\'ajout')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  const startEdit = (recipient) => {
    setEditingRecipient(recipient.id)
    setEditName(recipient.name)
    setError('')
    setSuccess('')
  }

  const cancelEdit = () => {
    setEditingRecipient(null)
    setEditName('')
  }

  const saveEdit = async (id) => {
    if (!editName.trim()) return

    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/recipients/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ name: editName.trim() })
      })

      if (response.ok) {
        setEditingRecipient(null)
        setEditName('')
        setSuccess('Destinataire modifié avec succès !')
        await loadRecipients()
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de la modification')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  const deleteRecipient = async (id, name) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer le destinataire "${name}" ?`)) {
      return
    }

    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/recipients/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        setSuccess('Destinataire supprimé avec succès !')
        await loadRecipients()
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de la suppression')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  const createDefaultRecipients = async () => {
    const defaultRecipients = [
      '👨 Papa',
      '👩 Maman', 
      '👧 Enfant 1',
      '👦 Enfant 2',
      '🐕 Animal de compagnie',
      '👨‍👩‍👧‍👦 Toute la famille',
      '🏠 Maison/Logement',
      '🎯 Autre'
    ]

    try {
      const token = authToken || localStorage.getItem('authToken')
      
      for (const recipientName of defaultRecipients) {
        await fetch(`${apiBase}/recipients`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ name: recipientName })
        })
      }
      
      await loadRecipients()
      setSuccess('Destinataires par défaut créés avec succès !')
    } catch (err) {
      setError('Erreur lors de la création des destinataires par défaut')
    }
  }

  return (
    <div className="card">
      <h3>👥 Gestion des destinataires</h3>
      <p className="help-text">
        Gérez les destinataires de vos dépenses (famille, individus, etc.)
      </p>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {success && (
        <div className="success-message">
          {success}
        </div>
      )}

      {/* Formulaire d'ajout */}
      <form onSubmit={addRecipient} className="form">
        <div className="row">
          <label htmlFor="newRecipient">👤 Nouveau destinataire</label>
          <div className="input-group">
            <input
              id="newRecipient"
              type="text"
              value={newRecipientName}
              onChange={e => setNewRecipientName(e.target.value)}
              placeholder="ex: Papa, Maman, Toute la famille..."
              maxLength="100"
              disabled={loading}
            />
            <button
              type="submit"
              disabled={loading || !newRecipientName.trim()}
              className="success"
            >
              {loading ? '⏳' : '➕ Ajouter'}
            </button>
          </div>
        </div>
      </form>

      {/* Bouton pour créer les destinataires par défaut */}
      {recipients.length === 0 && (
        <div className="actions">
          <button
            onClick={createDefaultRecipients}
            disabled={loading}
            className="info"
          >
            🎯 Créer les destinataires par défaut
          </button>
        </div>
      )}

      {/* Liste des destinataires */}
      {recipients.length > 0 && (
        <div className="list">
          <h4>📋 Destinataires existants ({recipients.length})</h4>
          {recipients.map(recipient => (
            <div key={recipient.id} className="list-item">
              {editingRecipient === recipient.id ? (
                <div className="edit-form">
                  <input
                    type="text"
                    value={editName}
                    onChange={e => setEditName(e.target.value)}
                    maxLength="100"
                    disabled={loading}
                  />
                  <div className="actions">
                    <button
                      onClick={() => saveEdit(recipient.id)}
                      disabled={loading || !editName.trim()}
                      className="success small"
                    >
                      ✅ Sauver
                    </button>
                    <button
                      onClick={cancelEdit}
                      disabled={loading}
                      className="secondary small"
                    >
                      ❌ Annuler
                    </button>
                  </div>
                </div>
              ) : (
                <div className="item-content">
                  <span className="name">{recipient.name}</span>
                  <div className="actions">
                    <button
                      onClick={() => startEdit(recipient)}
                      disabled={loading}
                      className="secondary small"
                    >
                      ✏️ Modifier
                    </button>
                    <button
                      onClick={() => deleteRecipient(recipient.id, recipient.name)}
                      disabled={loading}
                      className="danger small"
                    >
                      🗑️ Supprimer
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
