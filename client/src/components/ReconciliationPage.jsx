import React, { useState, useEffect } from 'react'

export default function ReconciliationPage({ apiBase, authToken }) {
  const [unreconciledTransactions, setUnreconciledTransactions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedTransactions, setSelectedTransactions] = useState(new Set())
  const [filters, setFilters] = useState({
    dateFrom: new Date().toISOString().split('T')[0], // Date du jour
    dateTo: new Date().toISOString().split('T')[0],   // Date du jour
    minAmount: '0',
    maxAmount: '10000', // Valeur par défaut élevée
    type: 'out' // Dépenses par défaut
  })

  useEffect(() => {
    loadUnreconciledTransactions()
  }, [])

  const loadUnreconciledTransactions = async () => {
    try {
      setLoading(true)
      setError('')
      const token = authToken || localStorage.getItem('authToken')

      console.log('Chargement des transactions non pointées...', { apiBase, token: token ? 'présent' : 'absent' })

      const response = await fetch(`${apiBase}/transactions/unreconciled`, {
        headers: { 'Authorization': `Bearer ${token}` }
      })

      console.log('Réponse reçue:', response.status, response.statusText)

      if (response.ok) {
        const data = await response.json()
        console.log('Données reçues:', data)
        setUnreconciledTransactions(data)
      } else {
        const errorData = await response.json().catch(() => ({}))
        console.error('Erreur API:', response.status, errorData)
        setError(`Erreur lors du chargement des transactions: ${errorData.error || response.statusText}`)
      }
    } catch (error) {
      console.error('Erreur de connexion:', error)
      setError(`Erreur de connexion au serveur: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const reconcileTransaction = async (transactionId) => {
    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/transactions/${transactionId}/reconcile`, {
        method: 'PUT',
        headers: { 'Authorization': `Bearer ${token}` }
      })

      if (response.ok) {
        // Retirer la transaction de la liste
        setUnreconciledTransactions(prev => 
          prev.filter(t => t.id !== transactionId)
        )
        setSelectedTransactions(prev => {
          const newSet = new Set(prev)
          newSet.delete(transactionId)
          return newSet
        })
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors du pointage')
      }
    } catch (error) {
      setError('Erreur de connexion au serveur')
    }
  }

  const reconcileSelectedTransactions = async () => {
    if (selectedTransactions.size === 0) return

    const promises = Array.from(selectedTransactions).map(id => reconcileTransaction(id))
    await Promise.all(promises)
    setSelectedTransactions(new Set())
  }

  const toggleTransactionSelection = (transactionId) => {
    setSelectedTransactions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(transactionId)) {
        newSet.delete(transactionId)
      } else {
        newSet.add(transactionId)
      }
      return newSet
    })
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const filteredTransactions = unreconciledTransactions.filter(transaction => {
    const transactionDate = new Date(transaction.date_iso)
    const today = new Date()
    today.setHours(23, 59, 59, 999) // Fin de la journée
    const amount = transaction.amount

    // Exclure les transactions futures (ne peuvent pas être pointées)
    if (transactionDate > today) return false

    // Filtre par date
    if (filters.dateFrom && transactionDate < new Date(filters.dateFrom)) return false
    if (filters.dateTo && transactionDate > new Date(filters.dateTo)) return false

    // Filtre par montant
    if (filters.minAmount && amount < parseFloat(filters.minAmount)) return false
    if (filters.maxAmount && amount > parseFloat(filters.maxAmount)) return false

    // Filtre par type
    if (filters.type !== 'all' && transaction.type !== filters.type) return false

    return true
  })

  if (loading) {
    return (
      <div className="card">
        <div className="loading">Chargement des transactions...</div>
      </div>
    )
  }

  return (
    <div className="card">
      <h3>✅ Pointage des transactions</h3>
      <p style={{ color: '#666', marginBottom: '20px' }}>
        Validez vos transactions avec votre relevé bancaire. Une fois pointées, elles ne pourront plus être modifiées ou supprimées.
        <br />
        <em style={{ fontSize: '0.9rem', color: '#888' }}>
          Note : Les transactions futures ne peuvent pas être pointées et n'apparaissent pas dans cette liste.
        </em>
      </p>

      {error && (
        <div className="error-message" style={{ marginBottom: '20px' }}>
          {error}
        </div>
      )}

      {/* Filtres */}
      <div className="filters" style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
        gap: '10px', 
        marginBottom: '20px',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px'
      }}>
        <div>
          <label style={{ fontSize: '12px', color: '#666' }}>Date de début</label>
          <input
            type="date"
            value={filters.dateFrom}
            onChange={e => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
            style={{ width: '100%', fontSize: '12px' }}
          />
        </div>
        <div>
          <label style={{ fontSize: '12px', color: '#666' }}>Date de fin</label>
          <input
            type="date"
            value={filters.dateTo}
            onChange={e => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
            style={{ width: '100%', fontSize: '12px' }}
          />
        </div>
        <div>
          <label style={{ fontSize: '12px', color: '#666' }}>Montant min</label>
          <input
            type="number"
            step="0.01"
            value={filters.minAmount}
            onChange={e => setFilters(prev => ({ ...prev, minAmount: e.target.value }))}
            placeholder="0.00"
            style={{ width: '100%', fontSize: '12px' }}
          />
        </div>
        <div>
          <label style={{ fontSize: '12px', color: '#666' }}>Montant max</label>
          <input
            type="number"
            step="0.01"
            value={filters.maxAmount}
            onChange={e => setFilters(prev => ({ ...prev, maxAmount: e.target.value }))}
            placeholder="999.99"
            style={{ width: '100%', fontSize: '12px' }}
          />
        </div>
        <div>
          <label style={{ fontSize: '12px', color: '#666' }}>Type</label>
          <select
            value={filters.type}
            onChange={e => setFilters(prev => ({ ...prev, type: e.target.value }))}
            style={{ width: '100%', fontSize: '12px' }}
          >
            <option value="all">Tous</option>
            <option value="in">Entrées</option>
            <option value="out">Dépenses</option>
          </select>
        </div>
      </div>

      {/* Actions groupées */}
      {selectedTransactions.size > 0 && (
        <div style={{ marginBottom: '20px', textAlign: 'center' }}>
          <button
            onClick={reconcileSelectedTransactions}
            className="success"
            style={{ marginRight: '10px' }}
          >
            ✅ Pointer {selectedTransactions.size} transaction(s) sélectionnée(s)
          </button>
          <button
            onClick={() => setSelectedTransactions(new Set())}
            className="secondary"
          >
            ❌ Désélectionner tout
          </button>
        </div>
      )}

      {filteredTransactions.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
          <div style={{ fontSize: '3rem', marginBottom: '16px' }}>✅</div>
          <p>Aucune transaction à pointer</p>
          <p style={{ fontSize: '0.9rem' }}>
            {unreconciledTransactions.length === 0 
              ? 'Toutes vos transactions sont déjà pointées !' 
              : 'Aucune transaction ne correspond aux filtres sélectionnés.'}
          </p>
        </div>
      ) : (
        <div className="table-container">
          <table>
            <thead>
              <tr>
                <th style={{ width: '40px' }}>
                  <input
                    type="checkbox"
                    checked={selectedTransactions.size === filteredTransactions.length && filteredTransactions.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedTransactions(new Set(filteredTransactions.map(t => t.id)))
                      } else {
                        setSelectedTransactions(new Set())
                      }
                    }}
                  />
                </th>
                <th>Date</th>
                <th>Montant</th>
                <th>Type</th>
                <th>Catégorie</th>
                <th>Mode de paiement</th>
                <th>Description</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.map(transaction => (
                <tr key={transaction.id}>
                  <td>
                    <input
                      type="checkbox"
                      checked={selectedTransactions.has(transaction.id)}
                      onChange={() => toggleTransactionSelection(transaction.id)}
                    />
                  </td>
                  <td>{formatDate(transaction.date_iso)}</td>
                  <td style={{
                    fontWeight: '600',
                    color: transaction.type === 'in' ? '#4caf50' : '#f44336'
                  }}>
                    {transaction.type === 'in' ? '+' : '-'}{formatCurrency(transaction.amount)}
                  </td>
                  <td>
                    <span className={`badge ${transaction.type === 'in' ? 'income' : 'expense'}`}>
                      {transaction.type === 'in' ? '💰 Entrée' : '💸 Dépense'}
                    </span>
                  </td>
                  <td>{transaction.category_name || '—'}</td>
                  <td>
                    {transaction.payment_method_name ? (
                      <div>
                        <span className="payment-method-name">💳 {transaction.payment_method_name}</span>
                        {transaction.check_number && (
                          <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '2px' }}>
                            📝 N° {transaction.check_number}
                          </div>
                        )}
                      </div>
                    ) : '—'}
                  </td>
                  <td style={{
                    maxWidth: '200px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {transaction.description || '—'}
                  </td>
                  <td>
                    <button
                      onClick={() => reconcileTransaction(transaction.id)}
                      className="success"
                      style={{ fontSize: '12px', padding: '4px 8px' }}
                      title="Pointer cette transaction"
                    >
                      ✅ Pointer
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div style={{ marginTop: '20px', fontSize: '0.9rem', color: '#666' }}>
        <p>
          📊 {filteredTransactions.length} transaction(s) à pointer
          {unreconciledTransactions.length !== filteredTransactions.length && 
            ` (${unreconciledTransactions.length} au total)`}
        </p>
      </div>
    </div>
  )
}
