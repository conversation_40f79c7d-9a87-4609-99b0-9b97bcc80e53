import React, { useState } from 'react'

export default function TransactionForm({ categories, paymentMethods, recipients, onSaved, apiBase, authToken }){
  const [amount, setAmount] = useState('')
  const [type, setType] = useState('out')
  const [category, setCategory] = useState('')
  const [description, setDescription] = useState('')
  const [transactionDate, setTransactionDate] = useState(new Date().toISOString().split('T')[0]) // Format YYYY-MM-DD
  const [paymentMethod, setPaymentMethod] = useState('')
  const [checkNumber, setCheckNumber] = useState('')
  const [recipient, setRecipient] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const submit = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    const a = parseFloat(amount)
    if (Number.isNaN(a) || a <= 0) {
      setError('Veuillez saisir un montant valide supérieur à 0')
      setLoading(false)
      return
    }

    // Vérifier si un numéro de chèque est requis
    const selectedPaymentMethod = paymentMethods?.find(pm => pm.id == paymentMethod)
    if (selectedPaymentMethod?.requires_check_number === 1 && !checkNumber.trim()) {
      setError('Le numéro de chèque est requis pour ce mode de paiement')
      setLoading(false)
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')

      // Convertir la date locale en ISO avec l'heure à midi pour éviter les problèmes de fuseau horaire
      const dateIso = new Date(transactionDate + 'T12:00:00.000Z').toISOString()

      const response = await fetch(apiBase + '/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          amount: Math.abs(a),
          type,
          category_id: category || null,
          description: description.trim() || null,
          date_iso: dateIso,
          payment_method_id: paymentMethod || null,
          check_number: checkNumber.trim() || null,
          recipient_id: recipient || null
        })
      })

      if (response.ok) {
        setAmount('')
        setDescription('')
        setCategory('')
        setPaymentMethod('')
        setCheckNumber('')
        setRecipient('')
        setTransactionDate(new Date().toISOString().split('T')[0]) // Reset à aujourd'hui
        onSaved()
        // Message de succès temporaire
        setError('')
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de l\'enregistrement')
      }
    } catch (error) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form className="card form" onSubmit={submit}>
      <h3>➕ Ajouter une transaction</h3>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="row">
        <label htmlFor="amount">💰 Montant *</label>
        <input
          id="amount"
          type="number"
          step="0.01"
          min="0"
          value={amount}
          onChange={e => setAmount(e.target.value)}
          placeholder="ex: 12.50"
          required
          disabled={loading}
        />
      </div>

      <div className="row">
        <label htmlFor="type">📊 Type *</label>
        <select
          id="type"
          value={type}
          onChange={e => setType(e.target.value)}
          disabled={loading}
        >
          <option value="in">💰 Entrée d'argent</option>
          <option value="out">💸 Dépense</option>
        </select>
      </div>

      <div className="row">
        <label htmlFor="category">🏷️ Catégorie</label>
        <select
          id="category"
          value={category}
          onChange={e => setCategory(e.target.value)}
          disabled={loading}
        >
          <option value="">— Aucune catégorie —</option>
          {categories.map(c => (
            <option key={c.id} value={c.id}>{c.name}</option>
          ))}
        </select>
      </div>

      <div className="row">
        <label htmlFor="recipient">👤 Destinataire</label>
        <select
          id="recipient"
          value={recipient}
          onChange={e => setRecipient(e.target.value)}
          disabled={loading}
        >
          <option value="">— Aucun destinataire —</option>
          {recipients?.map(r => (
            <option key={r.id} value={r.id}>{r.name}</option>
          ))}
        </select>
      </div>

      <div className="row">
        <label htmlFor="description">📝 Note (optionnel)</label>
        <input
          id="description"
          type="text"
          value={description}
          onChange={e => setDescription(e.target.value)}
          placeholder="Description de la transaction..."
          maxLength="255"
          disabled={loading}
        />
      </div>

      <div className="row">
        <label htmlFor="paymentMethod">💳 Mode de paiement (optionnel)</label>
        <select
          id="paymentMethod"
          value={paymentMethod}
          onChange={e => {
            setPaymentMethod(e.target.value)
            // Reset check number when changing payment method
            if (e.target.value === '' || !paymentMethods?.find(pm => pm.id == e.target.value)?.requires_check_number) {
              setCheckNumber('')
            }
          }}
          disabled={loading}
        >
          <option value="">— Aucun mode de paiement —</option>
          {paymentMethods?.map(method => (
            <option key={method.id} value={method.id}>
              {method.name}
            </option>
          ))}
        </select>
      </div>

      {/* Champ numéro de chèque conditionnel */}
      {paymentMethod && paymentMethods?.find(pm => pm.id == paymentMethod)?.requires_check_number === 1 && (
        <div className="row">
          <label htmlFor="checkNumber">📝 Numéro de chèque *</label>
          <input
            id="checkNumber"
            type="text"
            value={checkNumber}
            onChange={e => setCheckNumber(e.target.value)}
            placeholder="ex: 1234567"
            required
            disabled={loading}
          />
        </div>
      )}

      <div className="row">
        <label htmlFor="transactionDate">📅 Date de la transaction</label>
        <input
          id="transactionDate"
          type="date"
          value={transactionDate}
          onChange={e => setTransactionDate(e.target.value)}
          disabled={loading}
        />
      </div>

      <div className="actions">
        <button
          type="submit"
          disabled={loading || !amount}
          className="success"
        >
          {loading ? '⏳ Enregistrement...' : '✅ Enregistrer'}
        </button>
      </div>
    </form>
  )
}
