import React, { useState } from 'react'

export default function TransactionList({ transactions, onDeleted, onEdit, apiBase, authToken }){
  const [showFutureTransactions, setShowFutureTransactions] = useState(false)
  const [sortField, setSortField] = useState('date_iso')
  const [sortDirection, setSortDirection] = useState('desc')
  const [filters, setFilters] = useState({
    search: '',
    dateFrom: '',
    dateTo: '',
    category: '',
    type: 'all', // 'all', 'in', 'out'
    reconciled: 'all', // 'all', 'reconciled', 'unreconciled'
    minAmount: '',
    maxAmount: ''
  })
  const [showFilters, setShowFilters] = useState(false)

  // Fonction de tri
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Fonction pour obtenir l'icône de tri
  const getSortIcon = (field) => {
    if (sortField !== field) return '↕️'
    return sortDirection === 'asc' ? '↑' : '↓'
  }

  // Fonction de réinitialisation des filtres
  const resetFilters = () => {
    setFilters({
      search: '',
      dateFrom: '',
      dateTo: '',
      category: '',
      type: 'all',
      reconciled: 'all',
      minAmount: '',
      maxAmount: ''
    })
    setSortField('date_iso')
    setSortDirection('desc')
  }

  const del = async (id) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette transaction ?')) return
    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/transactions/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (response.ok) {
        onDeleted()
      } else {
        alert('Erreur lors de la suppression')
      }
    } catch (error) {
      alert('Erreur de connexion')
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  // Séparer les transactions par date
  const today = new Date()
  today.setHours(23, 59, 59, 999)

  const pastTransactions = transactions.filter(t => new Date(t.date_iso) <= today)
  const futureTransactions = transactions.filter(t => new Date(t.date_iso) > today)

  // Transactions à afficher selon le filtre futur
  const baseTransactions = showFutureTransactions
    ? transactions
    : pastTransactions

  // Appliquer les filtres
  const filteredTransactions = baseTransactions.filter(transaction => {
    // Filtre par recherche (description, catégorie ou destinataire)
    if (filters.search && !transaction.description.toLowerCase().includes(filters.search.toLowerCase()) &&
        !(transaction.category_name && transaction.category_name.toLowerCase().includes(filters.search.toLowerCase())) &&
        !(transaction.recipient_name && transaction.recipient_name.toLowerCase().includes(filters.search.toLowerCase()))) {
      return false
    }

    // Filtre par date
    if (filters.dateFrom && new Date(transaction.date_iso) < new Date(filters.dateFrom)) return false
    if (filters.dateTo && new Date(transaction.date_iso) > new Date(filters.dateTo + 'T23:59:59')) return false

    // Filtre par catégorie
    if (filters.category && transaction.category_name !== filters.category) return false

    // Filtre par type
    if (filters.type !== 'all' && transaction.type !== filters.type) return false

    // Filtre par statut de pointage
    if (filters.reconciled === 'reconciled' && transaction.is_reconciled !== 1) return false
    if (filters.reconciled === 'unreconciled' && transaction.is_reconciled === 1) return false

    // Filtre par montant
    if (filters.minAmount && transaction.amount < parseFloat(filters.minAmount)) return false
    if (filters.maxAmount && transaction.amount > parseFloat(filters.maxAmount)) return false

    return true
  })

  // Appliquer le tri
  const sortedTransactions = [...filteredTransactions].sort((a, b) => {
    let aValue, bValue

    switch (sortField) {
      case 'date_iso':
        aValue = new Date(a.date_iso)
        bValue = new Date(b.date_iso)
        break
      case 'amount':
        aValue = a.amount
        bValue = b.amount
        break
      case 'type':
        aValue = a.type
        bValue = b.type
        break
      case 'category_name':
        aValue = a.category_name || ''
        bValue = b.category_name || ''
        break
      case 'recipient_name':
        aValue = a.recipient_name || ''
        bValue = b.recipient_name || ''
        break
      case 'description':
        aValue = a.description || ''
        bValue = b.description || ''
        break
      case 'is_reconciled':
        aValue = a.is_reconciled || 0
        bValue = b.is_reconciled || 0
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const displayedTransactions = sortedTransactions

  // Obtenir la liste unique des catégories pour le filtre
  const uniqueCategories = [...new Set(transactions.map(t => t.category_name).filter(Boolean))].sort()

  if (!transactions || transactions.length === 0) {
    return (
      <div className="card list">
        <h3>📊 Transactions récentes</h3>
        <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
          <div style={{ fontSize: '3rem', marginBottom: '16px' }}>📝</div>
          <p>Aucune transaction pour le moment</p>
          <p style={{ fontSize: '0.9rem' }}>Ajoutez votre première transaction ci-dessus !</p>
        </div>
      </div>
    )
  }

  return (
    <div className="card list">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <h3>📊 Historique des transactions ({displayedTransactions.length})</h3>
        {futureTransactions.length > 0 && (
          <label style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '0.9rem',
            cursor: 'pointer',
            color: '#666'
          }}>
            <input
              type="checkbox"
              checked={showFutureTransactions}
              onChange={(e) => setShowFutureTransactions(e.target.checked)}
              style={{ cursor: 'pointer' }}
            />
            Afficher les transactions futures ({futureTransactions.length})
          </label>
        )}
      </div>

      {/* Section des filtres */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
          <button
            onClick={() => setShowFilters(!showFilters)}
            style={{
              padding: '8px 16px',
              backgroundColor: '#4a4a4a',
              color: 'white',
              border: '1px solid #666',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            {showFilters ? '🔽' : '▶️'} Filtres et recherche
          </button>

          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <span style={{ fontSize: '14px', color: '#666' }}>
              {displayedTransactions.length} transaction{displayedTransactions.length > 1 ? 's' : ''} affichée{displayedTransactions.length > 1 ? 's' : ''}
            </span>
            {(filters.search || filters.dateFrom || filters.dateTo || filters.category || filters.type !== 'all' ||
              filters.reconciled !== 'all' || filters.minAmount || filters.maxAmount || sortField !== 'date_iso' || sortDirection !== 'desc') && (
              <button
                onClick={resetFilters}
                style={{
                  padding: '6px 12px',
                  backgroundColor: '#ff6b6b',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
                title="Réinitialiser tous les filtres et le tri"
              >
                🔄 Reset
              </button>
            )}
          </div>
        </div>

        {showFilters && (
          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '16px',
            borderRadius: '8px',
            border: '1px solid #e9ecef'
          }}>
            {/* Première ligne de filtres */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '12px', marginBottom: '12px' }}>
              {/* Recherche */}
              <div>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                  🔍 Recherche
                </label>
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => setFilters({...filters, search: e.target.value})}
                  placeholder="Description ou catégorie..."
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                />
              </div>

              {/* Type */}
              <div>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                  💰 Type
                </label>
                <select
                  value={filters.type}
                  onChange={(e) => setFilters({...filters, type: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                >
                  <option value="all">Tous</option>
                  <option value="in">Revenus</option>
                  <option value="out">Dépenses</option>
                </select>
              </div>

              {/* Catégorie */}
              <div>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                  🏷️ Catégorie
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters({...filters, category: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                >
                  <option value="">Toutes</option>
                  {uniqueCategories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
              </div>

              {/* Pointage */}
              <div>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                  ✅ Pointage
                </label>
                <select
                  value={filters.reconciled}
                  onChange={(e) => setFilters({...filters, reconciled: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                >
                  <option value="all">Toutes</option>
                  <option value="reconciled">Pointées</option>
                  <option value="unreconciled">Non pointées</option>
                </select>
              </div>
            </div>

            {/* Deuxième ligne de filtres */}
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '12px' }}>
              {/* Date de début */}
              <div>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                  📅 Date de début
                </label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters({...filters, dateFrom: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                />
              </div>

              {/* Date de fin */}
              <div>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                  📅 Date de fin
                </label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => setFilters({...filters, dateTo: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                />
              </div>

              {/* Montant minimum */}
              <div>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                  💶 Montant min
                </label>
                <input
                  type="number"
                  value={filters.minAmount}
                  onChange={(e) => setFilters({...filters, minAmount: e.target.value})}
                  placeholder="0"
                  step="0.01"
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                />
              </div>

              {/* Montant maximum */}
              <div>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: 'bold', marginBottom: '4px', color: '#555' }}>
                  💶 Montant max
                </label>
                <input
                  type="number"
                  value={filters.maxAmount}
                  onChange={(e) => setFilters({...filters, maxAmount: e.target.value})}
                  placeholder="∞"
                  step="0.01"
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                />
              </div>
            </div>

            {/* Aide rapide */}
            <div style={{ marginTop: '12px', padding: '8px', backgroundColor: '#e3f2fd', borderRadius: '4px', fontSize: '12px', color: '#1976d2' }}>
              💡 <strong>Astuces :</strong> Cliquez sur les en-têtes de colonnes pour trier • Utilisez la recherche pour filtrer par description ou catégorie •
              Le bouton Reset remet tout à zéro
            </div>
          </div>
        )}
      </div>

      {displayedTransactions.length === 0 && transactions.length > 0 ? (
        <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
          <div style={{ fontSize: '3rem', marginBottom: '16px' }}>🔍</div>
          <p><strong>Aucune transaction ne correspond aux filtres</strong></p>
          <p style={{ fontSize: '0.9rem' }}>Essayez de modifier vos critères de recherche ou cliquez sur "Reset"</p>
        </div>
      ) : (
        <div className="table-container">
          <table>
          <thead>
            <tr>
              <th>
                <button
                  onClick={() => handleSort('date_iso')}
                  style={{ background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold', fontSize: '14px' }}
                  title="Trier par date"
                >
                  Date {getSortIcon('date_iso')}
                </button>
              </th>
              <th>
                <button
                  onClick={() => handleSort('amount')}
                  style={{ background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold', fontSize: '14px' }}
                  title="Trier par montant"
                >
                  Montant {getSortIcon('amount')}
                </button>
              </th>
              <th>
                <button
                  onClick={() => handleSort('type')}
                  style={{ background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold', fontSize: '14px' }}
                  title="Trier par type"
                >
                  Type {getSortIcon('type')}
                </button>
              </th>
              <th>
                <button
                  onClick={() => handleSort('category_name')}
                  style={{ background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold', fontSize: '14px' }}
                  title="Trier par catégorie"
                >
                  Catégorie {getSortIcon('category_name')}
                </button>
              </th>
              <th>
                <button
                  onClick={() => handleSort('recipient_name')}
                  style={{ background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold', fontSize: '14px' }}
                  title="Trier par destinataire"
                >
                  Destinataire {getSortIcon('recipient_name')}
                </button>
              </th>
              <th>
                <button
                  onClick={() => handleSort('payment_method_name')}
                  style={{ background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold', fontSize: '14px' }}
                  title="Trier par mode de paiement"
                >
                  Mode de paiement {getSortIcon('payment_method_name')}
                </button>
              </th>
              <th>
                <button
                  onClick={() => handleSort('description')}
                  style={{ background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold', fontSize: '14px' }}
                  title="Trier par description"
                >
                  Note {getSortIcon('description')}
                </button>
              </th>
              <th>
                <button
                  onClick={() => handleSort('is_reconciled')}
                  style={{ background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold', fontSize: '14px' }}
                  title="Trier par statut de pointage"
                >
                  Pointage {getSortIcon('is_reconciled')}
                </button>
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {displayedTransactions.map(t => {
              const isReconciled = t.is_reconciled === 1
              const isFuture = new Date(t.date_iso) > today
              return (
                <tr key={t.id} style={{
                  opacity: isReconciled ? 0.7 : 1,
                  backgroundColor: isFuture ? '#f0f8ff' : 'transparent',
                  fontStyle: isFuture ? 'italic' : 'normal'
                }}>
                  <td>
                    {formatDate(t.date_iso)}
                    {isFuture && <span style={{ marginLeft: '8px', fontSize: '0.8rem', color: '#2196f3' }}>🔮</span>}
                  </td>
                  <td style={{
                    fontWeight: '600',
                    color: t.type === 'in' ? '#4caf50' : '#f44336'
                  }}>
                    {t.type === 'in' ? '+' : '-'}{formatCurrency(t.amount)}
                  </td>
                  <td>
                    <span className={`badge ${t.type === 'in' ? 'income' : 'expense'}`}>
                      {t.type === 'in' ? '💰 Entrée' : '💸 Dépense'}
                    </span>
                  </td>
                  <td>{t.category_name || '—'}</td>
                  <td>{t.recipient_name || '—'}</td>
                  <td>
                    {t.payment_method_name ? (
                      <div>
                        <span className="payment-method-name">💳 {t.payment_method_name}</span>
                        {t.check_number && (
                          <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '2px' }}>
                            📝 N° {t.check_number}
                          </div>
                        )}
                      </div>
                    ) : '—'}
                  </td>
                  <td style={{
                    maxWidth: '200px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>
                    {t.description || '—'}
                  </td>
                  <td>
                    {isReconciled ? (
                      <span
                        className="badge"
                        style={{
                          backgroundColor: '#4caf50',
                          color: 'white',
                          fontSize: '11px'
                        }}
                        title={`Pointée le ${formatDate(t.reconciled_date)}`}
                      >
                        ✅ Pointée
                      </span>
                    ) : (
                      <span
                        className="badge"
                        style={{
                          backgroundColor: '#ff9800',
                          color: 'white',
                          fontSize: '11px'
                        }}
                      >
                        ⏳ À pointer
                      </span>
                    )}
                  </td>
                  <td>
                    <div style={{ display: 'flex', gap: '4px' }}>
                      <button
                        onClick={() => onEdit && onEdit(t.id)}
                        className="primary"
                        style={{ fontSize: '12px', padding: '4px 8px' }}
                        title={isReconciled ? "Impossible de modifier une transaction pointée" : "Modifier cette transaction"}
                        disabled={isReconciled}
                      >
                        ✏️ Modif
                      </button>
                      <button
                        onClick={() => del(t.id)}
                        className="danger"
                        style={{ fontSize: '12px', padding: '4px 8px' }}
                        title={isReconciled ? "Impossible de supprimer une transaction pointée" : "Supprimer cette transaction"}
                        disabled={isReconciled}
                      >
                        🗑️ Suppr
                      </button>
                    </div>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
        </div>
      )}
    </div>
  )
}