# 🚀 Déploiement Ma Compta Perso - Installation par utilisateur

Ce dossier contient tous les scripts nécessaires pour déployer Ma Compta Perso avec une approche **"installation par utilisateur"**.

## 🎯 Principe : Une instance par utilisateur

Chaque utilisateur du PC aura :
- ✅ Sa propre installation dans `~/MaComptaPerso/`
- ✅ Ses données privées et séparées
- ✅ Son propre port d'accès (5173, 5174, 5175...)
- ✅ Pas de conflit avec les autres utilisateurs

## 📋 Scripts disponibles

### 👤 Pour les utilisateurs finaux

#### `install-macompta-no-sudo.sh` - Installation utilisateur (RECOMMANDÉ)
Installation individuelle pour chaque utilisateur, sans privilèges sudo.

```bash
# Installation en une ligne
curl -s https://raw.githubusercontent.com/ppaperso/MaComptaPerso/main/deployment/install-macompta-no-sudo.sh | bash -s -- --repo https://github.com/ppaperso/MaComptaPerso.git
```

#### `check-port.sh` - Vérification des ports
Vérifie et attribue automatiquement des ports libres pour éviter les conflits.

```bash
cd ~/MaComptaPerso
./deployment/check-port.sh
```

### 🔧 Pour les administrateurs

#### `deploy-for-users.sh` - Déploiement multi-utilisateurs
Script pour l'admin pour installer l'application sur plusieurs comptes utilisateur.

```bash
# Installer les prérequis système
sudo ./deploy-for-users.sh --install-prereqs

# Installer pour des utilisateurs spécifiques
sudo ./deploy-for-users.sh user1 user2 user3

# Installer pour tous les utilisateurs
sudo ./deploy-for-users.sh --all-users
```

#### `install-macompta.sh` - Installation automatique avec sudo
Script d'installation complète pour utilisateurs finaux.
```bash
./deployment/install-macompta.sh
```

**Fonctionnalités :**
- ✅ Vérification automatique des prérequis (Node.js, npm, git)
- ✅ Installation automatique des dépendances système
- ✅ Clonage et installation de l'application
- ✅ Construction pour la production
- ✅ Création des scripts de lancement
- ✅ Création d'une icône desktop
- ✅ Configuration initiale
- ✅ Script de désinstallation

**Systèmes supportés :**
- Fedora (dnf)
- Ubuntu/Debian (apt)
- Arch Linux (pacman)

### ▶️ `start-production.sh` - Démarrage production
Lance l'application en mode production optimisé.
```bash
./deployment/start-production.sh
```

**Fonctionnalités :**
- Vérification des prérequis
- Construction automatique si nécessaire
- Démarrage du serveur backend
- Démarrage du serveur de fichiers statiques
- Ouverture automatique du navigateur
- Logs centralisés

### ⏹️ `stop-production.sh` - Arrêt production
Arrête proprement l'application en production.
```bash
./deployment/stop-production.sh
```

**Fonctionnalités :**
- Arrêt propre via PIDs sauvegardés
- Arrêt forcé si nécessaire
- Nettoyage des fichiers temporaires
- Vérification finale

## 🎯 Scénarios d'utilisation

### 📦 Installation sur un nouveau système

**Pour un utilisateur final (recommandé) :**
```bash
# Télécharger et exécuter l'installateur
curl -O https://raw.githubusercontent.com/votre-repo/MaComptaPerso/main/deployment/install-macompta.sh
chmod +x install-macompta.sh
./install-macompta.sh
```

**Pour un développeur :**
```bash
# Cloner le projet
git clone https://github.com/votre-repo/MaComptaPerso.git
cd MaComptaPerso

# Utiliser les scripts de déploiement
./deployment/start-production.sh
```

### 🔄 Mise à jour d'une installation existante

```bash
cd ~/MaComptaPerso
./deployment/stop-production.sh
git pull origin main
npm run install:all
cd client && npm run build && cd ..
./deployment/start-production.sh
```

### 🗑️ Désinstallation

```bash
# Si installé via install-macompta.sh
~/MaComptaPerso/uninstall.sh

# Ou manuellement
./deployment/stop-production.sh
rm -rf ~/MaComptaPerso
rm -f ~/Desktop/MaComptaPerso.desktop
rm -f ~/.local/share/applications/MaComptaPerso.desktop
```

## 🏗️ Architecture de déploiement

```
Production Deployment
├── Backend Server (Node.js)     → Port 3333
├── Static File Server (Vite)    → Port 5173
├── SQLite Database              → server/data/
├── Automatic Backups           → server/backups/
└── Desktop Integration         → ~/.local/share/applications/
```

## 📊 Fichiers générés

### Lors de l'installation
```
~/MaComptaPerso/
├── start-app.sh           # Script de démarrage utilisateur
├── stop-app.sh            # Script d'arrêt utilisateur
├── uninstall.sh           # Script de désinstallation
├── icon.png               # Icône de l'application
├── server/.env            # Configuration production
└── app.log                # Logs de l'application
```

### Icône desktop
```
~/Desktop/MaComptaPerso.desktop
~/.local/share/applications/MaComptaPerso.desktop
```

## 🔧 Configuration

### Variables d'environnement (server/.env)
```env
PORT=3333                    # Port du serveur backend
NODE_ENV=production          # Mode de fonctionnement
DB_PATH=./data/database.sqlite  # Chemin de la base de données
```

### Ports utilisés
- **3333** : API Backend (Express)
- **5173** : Interface Web (Vite Preview)

## 🚨 Prérequis système

### Logiciels requis
- **Node.js** >= 16.0.0
- **npm** >= 8.0.0
- **Git** (pour l'installation)

### Ressources système
- **RAM** : 2 GB minimum, 4 GB recommandé
- **Disque** : 500 MB pour l'application + données
- **Réseau** : Accès Internet pour l'installation uniquement

## 🔐 Sécurité

### Recommandations
- ✅ Changer le mot de passe par défaut (admin/password)
- ✅ Configurer un firewall pour bloquer les ports externes
- ✅ Sauvegarder régulièrement les données
- ✅ Ne pas exposer l'application sur Internet sans sécurisation

### Accès réseau local
Pour permettre l'accès depuis d'autres machines :
```bash
# Modifier start-production.sh
npx vite preview --port 5173 --host 0.0.0.0
```

⚠️ **Attention** : Cela rend l'application accessible depuis tout le réseau local.

## 📞 Support et dépannage

### Logs importants
- `production.log` - Logs de l'application
- `server/data/database.sqlite` - Base de données
- `server/backups/` - Sauvegardes automatiques

### Commandes de diagnostic
```bash
# Vérifier les processus
ps aux | grep -E "(node|vite)"

# Vérifier les ports
netstat -tlnp | grep -E ":(3333|5173)"

# Vérifier les logs
tail -f production.log
```
