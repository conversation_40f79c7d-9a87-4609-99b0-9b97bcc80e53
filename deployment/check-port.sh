#!/bin/bash
# check-port.sh - Vérification et attribution automatique des ports

echo "🔍 Vérification des ports pour $(whoami)"
echo "========================================"

# Ports de base
BASE_CLIENT_PORT=5173
BASE_SERVER_PORT=3333

# Fonction pour trouver un port libre
find_free_port() {
    local base_port=$1
    local port=$base_port
    
    while netstat -tlnp 2>/dev/null | grep -q ":$port "; do
        port=$((port + 1))
    done
    
    echo $port
}

# Trouver les ports libres
CLIENT_PORT=$(find_free_port $BASE_CLIENT_PORT)
SERVER_PORT=$(find_free_port $BASE_SERVER_PORT)

echo "📊 État des ports :"
echo "  Client (frontend) : $CLIENT_PORT"
echo "  Serveur (backend) : $SERVER_PORT"

# Vérifier si l'application est déjà lancée pour cet utilisateur
if pgrep -f "node.*index.js" > /dev/null; then
    echo ""
    echo "⚠️  Une instance semble déjà lancée pour votre utilisateur"
    echo "🔍 Processus actifs :"
    ps aux | grep -E "(node.*index.js|vite.*preview)" | grep -v grep | while read line; do
        echo "   $line"
    done
    
    echo ""
    echo "🌐 Tentative d'accès aux URLs courantes :"
    for port in 5173 5174 5175 5176; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            echo "   http://localhost:$port (port $port utilisé)"
        fi
    done
else
    echo ""
    echo "✅ Aucune instance détectée pour votre utilisateur"
fi

# Créer un fichier de configuration avec les ports
if [ -d "server" ]; then
    echo ""
    echo "⚙️  Mise à jour de la configuration..."
    
    # Mettre à jour le .env avec le bon port serveur
    if [ -f "server/.env" ]; then
        sed -i "s/PORT=.*/PORT=$SERVER_PORT/" server/.env
        echo "   ✅ Port serveur mis à jour : $SERVER_PORT"
    fi
    
    # Créer un script de démarrage personnalisé
    cat > "start-user.sh" << EOF
#!/bin/bash
# Script de démarrage personnalisé pour $(whoami)

APP_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
cd "\$APP_DIR"

echo "🚀 Démarrage de Ma Compta Perso pour $(whoami)..."
echo "   Serveur : http://localhost:$SERVER_PORT"
echo "   Client  : http://localhost:$CLIENT_PORT"

# Arrêter les processus existants de cet utilisateur
pkill -f "node.*index.js" 2>/dev/null || true
pkill -f "vite.*preview" 2>/dev/null || true

# Démarrer le serveur backend
echo "🔧 Démarrage du serveur sur le port $SERVER_PORT..."
cd server
PORT=$SERVER_PORT node index.js > ../app.log 2>&1 &
SERVER_PID=\$!
cd ..

# Attendre que le serveur démarre
sleep 3

# Démarrer le client
echo "🌐 Démarrage du client sur le port $CLIENT_PORT..."
cd client
npx vite preview --port $CLIENT_PORT --host >> ../app.log 2>&1 &
CLIENT_PID=\$!
cd ..

# Sauvegarder les PIDs
echo \$SERVER_PID > server.pid
echo \$CLIENT_PID > client.pid

echo "✅ Application démarrée !"
echo "🌐 Accès : http://localhost:$CLIENT_PORT"
echo "📊 Logs : tail -f \$APP_DIR/app.log"

# Ouvrir le navigateur
sleep 2
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:$CLIENT_PORT &
elif command -v gnome-open &> /dev/null; then
    gnome-open http://localhost:$CLIENT_PORT &
elif command -v firefox &> /dev/null; then
    firefox http://localhost:$CLIENT_PORT &
fi
EOF
    
    chmod +x "start-user.sh"
    echo "   ✅ Script personnalisé créé : ./start-user.sh"
fi

echo ""
echo "🎯 Résumé pour $(whoami) :"
echo "  📁 Répertoire : $(pwd)"
echo "  🌐 URL client : http://localhost:$CLIENT_PORT"
echo "  🔧 URL serveur : http://localhost:$SERVER_PORT"
echo "  🚀 Démarrage : ./start-user.sh (ou ./start-app.sh)"
echo "  🛑 Arrêt : ./stop-app.sh"
