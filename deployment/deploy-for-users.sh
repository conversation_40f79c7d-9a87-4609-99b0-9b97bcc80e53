#!/bin/bash
# deploy-for-users.sh - Script pour l'admin pour déployer sur plusieurs utilisateurs

set -e

echo "🚀 Déploiement Ma Compta Perso - Multi-utilisateurs"
echo "=================================================="
echo "👤 Exécuté par : $(whoami)"
echo ""

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Vérifier les privilèges
if [ "$EUID" -ne 0 ]; then
    log_warning "Ce script doit être exécuté avec sudo pour installer sur plusieurs comptes"
    log_info "Usage: sudo $0 [utilisateur1] [utilisateur2] ..."
    log_info "Ou: $0 --help pour plus d'options"
    exit 1
fi

# Aide
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: sudo $0 [OPTIONS] [UTILISATEURS...]"
    echo ""
    echo "Options:"
    echo "  --help, -h          Afficher cette aide"
    echo "  --list-users        Lister les utilisateurs du système"
    echo "  --install-prereqs   Installer les prérequis système"
    echo "  --all-users         Installer pour tous les utilisateurs (sauf système)"
    echo ""
    echo "Exemples:"
    echo "  sudo $0 --install-prereqs                    # Installer Node.js, npm, git"
    echo "  sudo $0 user1 user2 user3                   # Installer pour des utilisateurs spécifiques"
    echo "  sudo $0 --all-users                         # Installer pour tous les utilisateurs"
    echo "  sudo $0 --list-users                        # Voir les utilisateurs disponibles"
    exit 0
fi

# Lister les utilisateurs
if [ "$1" = "--list-users" ]; then
    log_info "Utilisateurs du système (UID >= 1000) :"
    awk -F: '$3 >= 1000 && $3 < 65534 { print "  " $1 " (UID: " $3 ", Home: " $6 ")" }' /etc/passwd
    exit 0
fi

# Installer les prérequis
install_prereqs() {
    log_info "Installation des prérequis système..."
    
    if command -v dnf &> /dev/null; then
        # Fedora/RHEL
        dnf install -y nodejs npm git
    elif command -v apt &> /dev/null; then
        # Ubuntu/Debian
        curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
        apt-get install -y nodejs git
    elif command -v pacman &> /dev/null; then
        # Arch Linux
        pacman -S nodejs npm git
    else
        log_error "Distribution non supportée pour l'installation automatique"
        log_info "Installez manuellement : nodejs npm git"
        exit 1
    fi
    
    log_success "Prérequis installés"
    log_info "Node.js: $(node --version)"
    log_info "npm: $(npm --version)"
    log_info "Git: $(git --version)"
}

if [ "$1" = "--install-prereqs" ]; then
    install_prereqs
    exit 0
fi

# Fonction d'installation pour un utilisateur
install_for_user() {
    local username=$1
    local user_home
    
    # Vérifier que l'utilisateur existe
    if ! id "$username" &>/dev/null; then
        log_error "L'utilisateur '$username' n'existe pas"
        return 1
    fi
    
    user_home=$(eval echo ~$username)
    log_info "Installation pour $username (home: $user_home)"
    
    # Vérifier que le répertoire home existe et est accessible
    if [ ! -d "$user_home" ]; then
        log_error "Répertoire home '$user_home' introuvable"
        return 1
    fi
    
    # Créer le script d'installation temporaire
    local temp_script="/tmp/install-macompta-$username.sh"
    
    cat > "$temp_script" << 'EOF'
#!/bin/bash
set -e

# Essayer d'abord l'URL HTTPS publique, puis SSH si échec
REPO_URL="https://github.com/ppaperso/MaComptaPerso.git"
INSTALL_DIR="$HOME/MaComptaPerso"

echo "🚀 Installation pour $(whoami) dans $INSTALL_DIR"

# Supprimer l'installation existante si elle existe
if [ -d "$INSTALL_DIR" ]; then
    echo "⚠️  Suppression de l'installation existante..."
    rm -rf "$INSTALL_DIR"
fi

# Cloner le projet
echo "📦 Clonage du projet..."

# Essayer le clonage avec différentes méthodes
if ! git clone "$REPO_URL" "$INSTALL_DIR" 2>/dev/null; then
    echo "⚠️  Échec du clonage HTTPS, tentative sans authentification..."

    # Essayer avec git config pour désactiver l'authentification
    git config --global credential.helper ""

    if ! git clone "$REPO_URL" "$INSTALL_DIR" 2>/dev/null; then
        echo "❌ Impossible de cloner le repository"
        echo "💡 Vérifiez que le repository est public ou configurez l'authentification"
        exit 1
    fi
fi

cd "$INSTALL_DIR"

# Installer les dépendances
echo "📦 Installation des dépendances..."
npm run install:all

# Construire l'application
echo "🏗️  Construction de l'application..."
cd client && npm run build && cd ..

# Configuration
echo "⚙️  Configuration..."
mkdir -p server/data server/backups
cat > server/.env << 'ENVEOF'
PORT=3333
NODE_ENV=production
DB_PATH=./data/database.sqlite
ENVEOF

# Rendre les scripts exécutables
chmod +x deployment/*.sh

# Créer les liens symboliques
ln -sf deployment/start-production.sh start-app.sh
ln -sf deployment/stop-production.sh stop-app.sh

# Vérifier et configurer les ports
if [ -f "deployment/check-port.sh" ]; then
    chmod +x deployment/check-port.sh
    ./deployment/check-port.sh
fi

echo "✅ Installation terminée pour $(whoami)"
echo "🚀 Démarrage : cd ~/MaComptaPerso && ./start-app.sh"
echo "🌐 Accès : http://localhost:5173 (ou port attribué)"
EOF
    
    chmod +x "$temp_script"
    
    # Exécuter en tant qu'utilisateur
    if su - "$username" -c "$temp_script"; then
        log_success "Installation réussie pour $username"
        
        # Créer une icône desktop si possible
        local desktop_dir="$user_home/Desktop"
        if [ -d "$desktop_dir" ]; then
            create_desktop_icon "$username" "$user_home"
        fi
        
    else
        log_error "Échec de l'installation pour $username"
    fi
    
    # Nettoyer
    rm -f "$temp_script"
}

# Créer une icône desktop
create_desktop_icon() {
    local username=$1
    local user_home=$2
    local desktop_file="$user_home/Desktop/MaComptaPerso.desktop"
    
    cat > "$desktop_file" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Ma Compta Perso ($username)
Comment=Application de comptabilité personnelle
Exec=$user_home/MaComptaPerso/start-app.sh
Icon=$user_home/MaComptaPerso/icon.png
Terminal=false
Categories=Office;Finance;
StartupNotify=true
EOF
    
    chown "$username:$username" "$desktop_file"
    chmod +x "$desktop_file"
    
    log_success "Icône desktop créée pour $username"
}

# Traitement des arguments
if [ "$1" = "--all-users" ]; then
    # Installer pour tous les utilisateurs (UID >= 1000)
    log_info "Installation pour tous les utilisateurs du système..."
    
    while IFS=: read -r username _ uid _ _ home _; do
        if [ "$uid" -ge 1000 ] && [ "$uid" -lt 65534 ] && [ "$username" != "nobody" ]; then
            install_for_user "$username"
        fi
    done < /etc/passwd
    
elif [ $# -eq 0 ]; then
    log_error "Aucun utilisateur spécifié"
    log_info "Usage: sudo $0 [utilisateur1] [utilisateur2] ..."
    log_info "Ou: sudo $0 --all-users pour tous les utilisateurs"
    exit 1
else
    # Installer pour les utilisateurs spécifiés
    for username in "$@"; do
        install_for_user "$username"
    done
fi

echo ""
log_success "Déploiement terminé !"
echo "======================================"
log_info "Chaque utilisateur peut maintenant :"
echo "  1. Se connecter à son compte"
echo "  2. Ouvrir un terminal"
echo "  3. Exécuter : cd ~/MaComptaPerso && ./start-app.sh"
echo "  4. Ou double-cliquer sur l'icône 'Ma Compta Perso' du bureau"
echo ""
log_info "Chaque utilisateur aura :"
echo "  - Sa propre instance de l'application"
echo "  - Ses données privées dans ~/MaComptaPerso/server/data/"
echo "  - Ses sauvegardes dans ~/MaComptaPerso/server/backups/"
echo "  - Son propre port d'accès (5173, 5174, 5175, etc.)"
