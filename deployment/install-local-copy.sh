#!/bin/bash
# install-local-copy.sh - Installation en copiant les fichiers locaux
# Évite les problèmes d'authentification Git

set -e

if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <nom_utilisateur>"
    echo "Exemple: $0 Isa"
    exit 1
fi

USERNAME="$1"
SOURCE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TARGET_USER_HOME=$(eval echo ~$USERNAME)
TARGET_DIR="$TARGET_USER_HOME/MaComptaPerso"

echo "🚀 Installation locale pour $USERNAME"
echo "====================================="
echo "📁 Source : $SOURCE_DIR"
echo "📁 Cible : $TARGET_DIR"
echo ""

# Vérifier que l'utilisateur existe
if ! id "$USERNAME" &>/dev/null; then
    echo "❌ L'utilisateur '$USERNAME' n'existe pas"
    exit 1
fi

# Vérifier que le répertoire home existe
if [ ! -d "$TARGET_USER_HOME" ]; then
    echo "❌ Répertoire home '$TARGET_USER_HOME' introuvable"
    exit 1
fi

# Supprimer l'installation existante si elle existe
if [ -d "$TARGET_DIR" ]; then
    echo "⚠️  Suppression de l'installation existante..."
    rm -rf "$TARGET_DIR"
fi

# Créer le répertoire cible
echo "📁 Création du répertoire d'installation..."
mkdir -p "$TARGET_DIR"

# Copier les fichiers nécessaires
echo "📦 Copie des fichiers..."

# Copier la structure principale
cp -r "$SOURCE_DIR/client" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/server" "$TARGET_DIR/"
cp -r "$SOURCE_DIR/deployment" "$TARGET_DIR/"

# Copier les fichiers de configuration
cp "$SOURCE_DIR/package.json" "$TARGET_DIR/"
cp "$SOURCE_DIR/package-lock.json" "$TARGET_DIR/" 2>/dev/null || true
cp "$SOURCE_DIR/README.md" "$TARGET_DIR/" 2>/dev/null || true

# Nettoyer les fichiers de développement
echo "🧹 Nettoyage des fichiers de développement..."
rm -rf "$TARGET_DIR/client/node_modules" 2>/dev/null || true
rm -rf "$TARGET_DIR/server/node_modules" 2>/dev/null || true
rm -rf "$TARGET_DIR/node_modules" 2>/dev/null || true
rm -f "$TARGET_DIR"/*.log 2>/dev/null || true
rm -f "$TARGET_DIR"/*.pid 2>/dev/null || true

# Créer le script d'installation pour l'utilisateur
cat > "/tmp/setup-macompta-$USERNAME.sh" << 'SETUP_EOF'
#!/bin/bash
set -e

INSTALL_DIR="$HOME/MaComptaPerso"
cd "$INSTALL_DIR"

echo "🚀 Configuration pour $(whoami) dans $INSTALL_DIR"

# Installer les dépendances
echo "📦 Installation des dépendances..."
npm run install:all

# Construire l'application
echo "🏗️  Construction de l'application..."
cd client && npm run build && cd ..

# Configuration
echo "⚙️  Configuration..."
mkdir -p server/data server/backups

# Créer le fichier .env
cat > server/.env << 'ENV_EOF'
PORT=3333
NODE_ENV=production
DB_PATH=./data/database.sqlite
ENV_EOF

# Rendre les scripts exécutables
chmod +x deployment/*.sh

# Créer les liens symboliques
ln -sf deployment/start-production.sh start-app.sh
ln -sf deployment/stop-production.sh stop-app.sh

# Vérifier et configurer les ports
if [ -f "deployment/check-port.sh" ]; then
    chmod +x deployment/check-port.sh
    ./deployment/check-port.sh
fi

# Créer une icône simple
cat > icon.svg << 'ICON_EOF'
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="64" height="64" rx="12" fill="url(#grad)"/>
  <text x="32" y="42" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">€</text>
</svg>
ICON_EOF

cp icon.svg icon.png 2>/dev/null || true

echo "✅ Configuration terminée pour $(whoami)"
echo "🚀 Démarrage : ./start-app.sh"
echo "🌐 Accès : http://localhost:5173 (ou port attribué)"
SETUP_EOF

chmod +x "/tmp/setup-macompta-$USERNAME.sh"

# Changer le propriétaire de tous les fichiers
echo "🔐 Configuration des permissions..."
chown -R "$USERNAME:$USERNAME" "$TARGET_DIR"

# Exécuter la configuration en tant qu'utilisateur
echo "⚙️  Exécution de la configuration pour $USERNAME..."
if su - "$USERNAME" -c "/tmp/setup-macompta-$USERNAME.sh"; then
    echo "✅ Installation réussie pour $USERNAME"
    
    # Créer l'icône desktop
    create_desktop_icon "$USERNAME" "$TARGET_USER_HOME"
    
else
    echo "❌ Échec de la configuration pour $USERNAME"
    exit 1
fi

# Nettoyer
rm -f "/tmp/setup-macompta-$USERNAME.sh"

echo ""
echo "🎉 Installation terminée pour $USERNAME !"
echo "========================================="
echo "📁 Installation : $TARGET_DIR"
echo "👤 Utilisateur peut maintenant :"
echo "   1. Se connecter à son compte"
echo "   2. Exécuter : cd ~/MaComptaPerso && ./start-app.sh"
echo "   3. Accéder via : http://localhost:5173"

# Fonction pour créer l'icône desktop
create_desktop_icon() {
    local username=$1
    local user_home=$2
    local desktop_dir="$user_home/Desktop"
    
    if [ -d "$desktop_dir" ]; then
        local desktop_file="$desktop_dir/MaComptaPerso.desktop"
        
        cat > "$desktop_file" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Ma Compta Perso ($username)
Comment=Application de comptabilité personnelle
Exec=$user_home/MaComptaPerso/start-app.sh
Icon=$user_home/MaComptaPerso/icon.png
Terminal=false
Categories=Office;Finance;
StartupNotify=true
EOF
        
        chown "$username:$username" "$desktop_file"
        chmod +x "$desktop_file"
        
        echo "✅ Icône desktop créée pour $username"
    fi
}
