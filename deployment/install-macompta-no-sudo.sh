#!/bin/bash
# install-macompta-user.sh - Installation par utilisateur
# Chaque utilisateur a sa propre instance avec ses données privées

set -e

echo "🚀 Installation de Ma Compta Perso - Installation utilisateur"
echo "============================================================="
echo "👤 Utilisateur: $(whoami)"
echo "📁 Installation dans: $HOME/MaComptaPerso"
echo ""

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérification des prérequis (sans installation automatique)
check_requirements() {
    log_info "Vérification des prérequis..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé."
        log_error "Demandez à l'administrateur d'installer : sudo dnf install nodejs npm git"
        exit 1
    fi
    
    # Vérifier la version de Node.js
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        log_error "Node.js version $NODE_VERSION détectée. Version 16+ requise."
        log_error "Demandez à l'administrateur de mettre à jour Node.js"
        exit 1
    fi
    
    log_success "Node.js $(node --version) détecté"
    log_success "npm $(npm --version) détecté"
    
    # Vérifier Git
    if ! command -v git &> /dev/null; then
        log_error "Git n'est pas installé."
        log_error "Demandez à l'administrateur d'installer : sudo dnf install git"
        exit 1
    fi
    
    log_success "Git $(git --version | cut -d' ' -f3) détecté"
}

# Installation de l'application
install_app() {
    log_info "Téléchargement et installation..."
    
    # Créer le répertoire d'installation
    INSTALL_DIR="$HOME/MaComptaPerso"
    
    if [ -d "$INSTALL_DIR" ]; then
        log_warning "Le répertoire $INSTALL_DIR existe déjà"
        read -p "Voulez-vous le supprimer et réinstaller ? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$INSTALL_DIR"
        else
            log_error "Installation annulée"
            exit 1
        fi
    fi
    
    # Cloner le projet
    log_info "Clonage du projet..."
    if [ -n "$REPO_URL" ]; then
        git clone "$REPO_URL" "$INSTALL_DIR"
    else
        # Si pas d'URL fournie, copier depuis le répertoire courant
        if [ -f "package.json" ] && [ -d "client" ] && [ -d "server" ]; then
            cp -r . "$INSTALL_DIR"
            cd "$INSTALL_DIR"
            # Nettoyer les fichiers de développement
            rm -rf node_modules client/node_modules server/node_modules
            rm -f *.log *.pid
        else
            log_error "Impossible de trouver les sources. Définissez REPO_URL ou exécutez depuis le répertoire du projet."
            exit 1
        fi
    fi
    
    cd "$INSTALL_DIR"
    
    # Installer les dépendances
    log_info "Installation des dépendances..."
    npm run install:all
    
    # Construire le client pour la production
    log_info "Construction de l'application..."
    cd client && npm run build && cd ..
    
    log_success "Application installée dans $INSTALL_DIR"
}

# Créer les scripts de lancement
create_scripts() {
    log_info "Création des scripts de lancement..."
    
    # Script de démarrage
    cat > "$INSTALL_DIR/start-app.sh" << 'EOF'
#!/bin/bash
# Script de démarrage de Ma Compta Perso

APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$APP_DIR"

echo "🚀 Démarrage de Ma Compta Perso..."

# Arrêter les processus existants
pkill -f "node.*index.js" 2>/dev/null || true
pkill -f "vite.*preview" 2>/dev/null || true

# Démarrer le serveur backend
echo "🔧 Démarrage du serveur..."
cd server
node index.js > ../app.log 2>&1 &
SERVER_PID=$!
cd ..

# Attendre que le serveur démarre
sleep 3

# Démarrer le serveur de fichiers statiques
echo "🌐 Démarrage du client..."
cd client
npx vite preview --port 5173 --host >> ../app.log 2>&1 &
CLIENT_PID=$!
cd ..

# Sauvegarder les PIDs
echo $SERVER_PID > server.pid
echo $CLIENT_PID > client.pid

echo "✅ Application démarrée !"
echo "🌐 Accès : http://localhost:5173"
echo "📊 Logs : tail -f $APP_DIR/app.log"

# Ouvrir le navigateur
sleep 2
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5173 &
elif command -v gnome-open &> /dev/null; then
    gnome-open http://localhost:5173 &
elif command -v firefox &> /dev/null; then
    firefox http://localhost:5173 &
fi
EOF

    chmod +x "$INSTALL_DIR/start-app.sh"
    
    # Script d'arrêt
    cat > "$INSTALL_DIR/stop-app.sh" << 'EOF'
#!/bin/bash
# Script d'arrêt de Ma Compta Perso

APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$APP_DIR"

echo "🛑 Arrêt de Ma Compta Perso..."

# Arrêter via les PIDs sauvegardés
if [ -f "server.pid" ]; then
    SERVER_PID=$(cat server.pid)
    kill $SERVER_PID 2>/dev/null || true
    rm server.pid
fi

if [ -f "client.pid" ]; then
    CLIENT_PID=$(cat client.pid)
    kill $CLIENT_PID 2>/dev/null || true
    rm client.pid
fi

# Arrêt forcé si nécessaire
pkill -f "node.*index.js" 2>/dev/null || true
pkill -f "vite.*preview" 2>/dev/null || true

echo "✅ Application arrêtée"
EOF

    chmod +x "$INSTALL_DIR/stop-app.sh"
    
    log_success "Scripts de lancement créés"
}

# Créer une icône simple
create_icon() {
    log_info "Création de l'icône..."
    
    # Créer une icône SVG simple
    cat > "$INSTALL_DIR/icon.svg" << 'EOF'
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="64" height="64" rx="12" fill="url(#grad)"/>
  <text x="32" y="42" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="white" text-anchor="middle">€</text>
</svg>
EOF

    # Convertir en PNG si possible
    if command -v convert &> /dev/null; then
        convert "$INSTALL_DIR/icon.svg" "$INSTALL_DIR/icon.png" 2>/dev/null || cp "$INSTALL_DIR/icon.svg" "$INSTALL_DIR/icon.png"
    elif command -v rsvg-convert &> /dev/null; then
        rsvg-convert -w 64 -h 64 "$INSTALL_DIR/icon.svg" > "$INSTALL_DIR/icon.png"
    else
        cp "$INSTALL_DIR/icon.svg" "$INSTALL_DIR/icon.png"
    fi
    
    log_success "Icône créée"
}

# Créer l'icône desktop
create_desktop_icon() {
    log_info "Création de l'icône desktop..."
    
    # S'assurer que le bureau existe
    mkdir -p "$HOME/Desktop"
    
    DESKTOP_FILE="$HOME/Desktop/MaComptaPerso.desktop"
    
    cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Ma Compta Perso
Comment=Application de comptabilité personnelle
Exec=$INSTALL_DIR/start-app.sh
Icon=$INSTALL_DIR/icon.png
Terminal=false
Categories=Office;Finance;
StartupNotify=true
EOF

    chmod +x "$DESKTOP_FILE"
    
    # Créer aussi dans le menu applications
    APPS_DIR="$HOME/.local/share/applications"
    mkdir -p "$APPS_DIR"
    cp "$DESKTOP_FILE" "$APPS_DIR/"
    
    # Mettre à jour le cache des icônes si possible
    if command -v update-desktop-database &> /dev/null; then
        update-desktop-database "$APPS_DIR" 2>/dev/null || true
    fi
    
    log_success "Icône créée sur le bureau et dans le menu"
}

# Configuration initiale
setup_config() {
    log_info "Configuration initiale..."
    
    # Créer le fichier de configuration
    cat > "$INSTALL_DIR/server/.env" << 'EOF'
PORT=3333
NODE_ENV=production
DB_PATH=./data/database.sqlite
EOF

    # S'assurer que les répertoires existent
    mkdir -p "$INSTALL_DIR/server/data"
    mkdir -p "$INSTALL_DIR/server/backups"
    
    # Définir les permissions appropriées
    chmod 755 "$INSTALL_DIR/server/data"
    chmod 755 "$INSTALL_DIR/server/backups"
    
    log_success "Configuration terminée"
}

# Exécution principale
main() {
    echo "Ma Compta Perso - Installation sans sudo"
    echo "Version 1.0 - $(date)"
    echo ""
    
    check_requirements
    install_app
    create_scripts
    create_icon
    create_desktop_icon
    setup_config
    
    echo ""
    log_success "Installation terminée avec succès !"
    echo "======================================"
    echo "📍 Application installée dans : $INSTALL_DIR"
    echo "🖥️  Icône créée sur le bureau : MaComptaPerso"
    echo "🚀 Pour démarrer : Double-cliquez sur l'icône ou exécutez :"
    echo "   $INSTALL_DIR/start-app.sh"
    echo "🛑 Pour arrêter : $INSTALL_DIR/stop-app.sh"
    echo ""
    echo "🌐 L'application sera accessible sur : http://localhost:5173"
    echo "👤 Connexion par défaut : admin / password"
    echo ""
    
    # Proposer de démarrer immédiatement
    read -p "Voulez-vous démarrer l'application maintenant ? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        log_info "Démarrage de l'application..."
        "$INSTALL_DIR/start-app.sh"
    fi
}

# Gestion des arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "IMPORTANT: Ce script nécessite que Node.js, npm et git soient pré-installés"
    echo "Demandez à l'administrateur d'exécuter : sudo dnf install nodejs npm git"
    echo ""
    echo "Cette installation crée une instance privée pour chaque utilisateur :"
    echo "- Données séparées et privées"
    echo "- Pas de conflit entre utilisateurs"
    echo "- Chacun gère ses propres sauvegardes"
    echo ""
    echo "Options:"
    echo "  --help, -h     Afficher cette aide"
    echo "  --repo URL     URL du dépôt Git à cloner"
    echo ""
    echo "Variables d'environnement:"
    echo "  REPO_URL       URL du dépôt Git (alternative à --repo)"
    echo ""
    echo "Exemple:"
    echo "  $0 --repo https://github.com/ppaperso/MaComptaPerso.git"
    exit 0
fi

if [ "$1" = "--repo" ] && [ -n "$2" ]; then
    REPO_URL="$2"
fi

main "$@"
