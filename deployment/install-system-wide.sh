#!/bin/bash
# Installation système partagée (nécessite sudo)
# ATTENTION: Configuration complexe, utilisez seulement si nécessaire

set -e

if [ "$EUID" -ne 0 ]; then
    echo "❌ Ce script doit être exécuté avec sudo"
    exit 1
fi

echo "🚀 Installation système de Ma Compta Perso"
echo "=========================================="

# Répertoires
INSTALL_DIR="/opt/MaComptaPerso"
DATA_DIR="/var/lib/macompta"
LOG_DIR="/var/log/macompta"

# Créer les répertoires système
mkdir -p "$INSTALL_DIR"
mkdir -p "$DATA_DIR"
mkdir -p "$LOG_DIR"

# Cloner l'application
git clone https://github.com/ppaperso/MaComptaPerso.git "$INSTALL_DIR"
cd "$INSTALL_DIR"

# Installer les dépendances
npm run install:all
cd client && npm run build && cd ..

# Configuration système
cat > "$INSTALL_DIR/server/.env" << EOF
PORT=3333
NODE_ENV=production
DB_PATH=$DATA_DIR/\$USER/database.sqlite
BACKUP_DIR=$DATA_DIR/\$USER/backups
LOG_FILE=$LOG_DIR/\$USER.log
EOF

# Script de lancement utilisateur
cat > "/usr/local/bin/macompta" << 'EOF'
#!/bin/bash
# Script de lancement système pour Ma Compta Perso

USER_DATA_DIR="/var/lib/macompta/$USER"
USER_LOG_FILE="/var/log/macompta/$USER.log"

# Créer les répertoires utilisateur
mkdir -p "$USER_DATA_DIR/backups"
touch "$USER_LOG_FILE"

# Vérifier si l'application est déjà lancée
if pgrep -f "node.*index.js.*$USER" > /dev/null; then
    echo "⚠️  Ma Compta Perso est déjà lancée pour $USER"
    echo "🌐 Accès : http://localhost:3333"
    exit 0
fi

# Trouver un port libre pour chaque utilisateur
BASE_PORT=3333
USER_PORT=$((BASE_PORT + $(id -u) % 1000))

cd /opt/MaComptaPerso/server
PORT=$USER_PORT DB_PATH="$USER_DATA_DIR/database.sqlite" node index.js > "$USER_LOG_FILE" 2>&1 &

echo "✅ Ma Compta Perso démarrée pour $USER"
echo "🌐 Accès : http://localhost:$USER_PORT"
echo "📊 Logs : tail -f $USER_LOG_FILE"
EOF

chmod +x "/usr/local/bin/macompta"

# Permissions
chown -R root:root "$INSTALL_DIR"
chmod -R 755 "$INSTALL_DIR"
chmod 777 "$DATA_DIR"
chmod 777 "$LOG_DIR"

echo "✅ Installation système terminée"
echo "👥 Chaque utilisateur peut lancer : macompta"
echo "⚠️  ATTENTION: Configuration complexe, testez soigneusement"
