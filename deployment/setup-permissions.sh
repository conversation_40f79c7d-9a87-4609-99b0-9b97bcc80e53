#!/bin/bash
# setup-permissions.sh - Configuration des permissions pour tous les scripts

echo "🔧 Configuration des permissions des scripts de déploiement"
echo "=========================================================="

# Répertoire de déploiement
DEPLOY_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$DEPLOY_DIR"

echo "📁 Répertoire : $DEPLOY_DIR"
echo ""

# Rendre tous les scripts exécutables
echo "🔐 Configuration des permissions..."

scripts=(
    "install-macompta.sh"
    "install-macompta-no-sudo.sh"
    "deploy-for-users.sh"
    "check-port.sh"
    "start-production.sh"
    "stop-production.sh"
    "setup-permissions.sh"
)

for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        chmod +x "$script"
        echo "  ✅ $script"
    else
        echo "  ⚠️  $script (non trouvé)"
    fi
done

# Vérifier les permissions
echo ""
echo "📊 État des permissions :"
ls -la *.sh | while read -r line; do
    echo "  $line"
done

echo ""
echo "✅ Configuration terminée !"
echo ""
echo "🎯 Scripts prêts à utiliser :"
echo "  👤 Utilisateurs : ./install-macompta-no-sudo.sh"
echo "  🔧 Admin : sudo ./deploy-for-users.sh"
echo "  🔍 Vérification : ./check-port.sh"
