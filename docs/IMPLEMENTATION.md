# 🚀 Guide d'implémentation - Ma Compt<PERSON> Perso

## 📋 Vue d'ensemble

Ce guide vous explique comment installer et déployer **Ma Compta Perso** sur votre système Linux (Fedora, Ubuntu, etc.) pour une utilisation en production par un utilisateur final.

## 🎯 Objectifs du déploiement

- Installation simple et automatisée
- Lancement en un clic via une icône desktop
- Fonctionnement autonome sans connaissances techniques
- Accès via navigateur web local
- Données stockées localement et sécurisées

## 📋 Prérequis système

### Système d'exploitation supporté
- **Linux** : Fedora 35+, Ubuntu 20.04+, Debian 11+, CentOS 8+
- **Architecture** : x86_64 (64-bit)
- **RAM** : Minimum 2 GB, recommandé 4 GB
- **Espace disque** : 500 MB pour l'application + espace pour les données

### Logiciels requis
- **Node.js** : Version 16.0.0 ou supérieure
- **npm** : Version 8.0.0 ou supérieure
- **Git** : Pour le téléchargement du code source

## 🔧 Installation automatique

### Script d'installation (recommandé)

Créez le script d'installation suivant :

```bash
#!/bin/bash
# install-macompta.sh - Script d'installation automatique

set -e

echo "🚀 Installation de Ma Compta Perso"
echo "=================================="

# Vérification des prérequis
check_requirements() {
    echo "🔍 Vérification des prérequis..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js n'est pas installé"
        echo "📦 Installation de Node.js..."
        
        # Installation selon la distribution
        if command -v dnf &> /dev/null; then
            # Fedora/RHEL
            sudo dnf install -y nodejs npm
        elif command -v apt &> /dev/null; then
            # Ubuntu/Debian
            curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
            sudo apt-get install -y nodejs
        else
            echo "❌ Distribution non supportée. Installez Node.js manuellement."
            exit 1
        fi
    fi
    
    # Vérifier la version de Node.js
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        echo "❌ Node.js version $NODE_VERSION détectée. Version 16+ requise."
        exit 1
    fi
    
    echo "✅ Node.js $(node --version) détecté"
    echo "✅ npm $(npm --version) détecté"
}

# Installation de l'application
install_app() {
    echo "📦 Téléchargement et installation..."
    
    # Créer le répertoire d'installation
    INSTALL_DIR="$HOME/MaComptaPerso"
    
    if [ -d "$INSTALL_DIR" ]; then
        echo "⚠️  Le répertoire $INSTALL_DIR existe déjà"
        read -p "Voulez-vous le supprimer et réinstaller ? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$INSTALL_DIR"
        else
            echo "❌ Installation annulée"
            exit 1
        fi
    fi
    
    # Cloner le projet
    git clone https://github.com/ppauser/MaComptaPerso.git "$INSTALL_DIR"
    cd "$INSTALL_DIR"
    
    # Installer les dépendances
    echo "📦 Installation des dépendances..."
    npm run install:all
    
    # Construire le client pour la production
    echo "🏗️  Construction de l'application..."
    cd client && npm run build && cd ..
    
    echo "✅ Application installée dans $INSTALL_DIR"
}

# Créer les scripts de lancement
create_scripts() {
    echo "📝 Création des scripts de lancement..."
    
    # Script de démarrage
    cat > "$INSTALL_DIR/start-app.sh" << 'EOF'
#!/bin/bash
# Script de démarrage de Ma Compta Perso

APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$APP_DIR"

echo "🚀 Démarrage de Ma Compta Perso..."

# Arrêter les processus existants
pkill -f "node.*index.js" 2>/dev/null || true
pkill -f "vite.*preview" 2>/dev/null || true

# Démarrer le serveur backend
echo "🔧 Démarrage du serveur..."
cd server
node index.js > ../app.log 2>&1 &
SERVER_PID=$!
cd ..

# Attendre que le serveur démarre
sleep 3

# Démarrer le serveur de fichiers statiques
echo "🌐 Démarrage du client..."
cd client
npx vite preview --port 5173 --host >> ../app.log 2>&1 &
CLIENT_PID=$!
cd ..

# Sauvegarder les PIDs
echo $SERVER_PID > server.pid
echo $CLIENT_PID > client.pid

echo "✅ Application démarrée !"
echo "🌐 Accès : http://localhost:5173"
echo "📊 Logs : tail -f $APP_DIR/app.log"

# Ouvrir le navigateur
sleep 2
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5173
elif command -v gnome-open &> /dev/null; then
    gnome-open http://localhost:5173
fi
EOF

    chmod +x "$INSTALL_DIR/start-app.sh"
    
    # Script d'arrêt
    cat > "$INSTALL_DIR/stop-app.sh" << 'EOF'
#!/bin/bash
# Script d'arrêt de Ma Compta Perso

APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$APP_DIR"

echo "🛑 Arrêt de Ma Compta Perso..."

# Arrêter via les PIDs sauvegardés
if [ -f "server.pid" ]; then
    SERVER_PID=$(cat server.pid)
    kill $SERVER_PID 2>/dev/null || true
    rm server.pid
fi

if [ -f "client.pid" ]; then
    CLIENT_PID=$(cat client.pid)
    kill $CLIENT_PID 2>/dev/null || true
    rm client.pid
fi

# Arrêt forcé si nécessaire
pkill -f "node.*index.js" 2>/dev/null || true
pkill -f "vite.*preview" 2>/dev/null || true

echo "✅ Application arrêtée"
EOF

    chmod +x "$INSTALL_DIR/stop-app.sh"
}

# Créer l'icône desktop
create_desktop_icon() {
    echo "🖥️  Création de l'icône desktop..."
    
    DESKTOP_FILE="$HOME/Desktop/MaComptaPerso.desktop"
    
    cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Ma Compta Perso
Comment=Application de comptabilité personnelle
Exec=$INSTALL_DIR/start-app.sh
Icon=$INSTALL_DIR/icon.png
Terminal=false
Categories=Office;Finance;
StartupNotify=true
EOF

    chmod +x "$DESKTOP_FILE"
    
    # Créer aussi dans le menu applications
    APPS_DIR="$HOME/.local/share/applications"
    mkdir -p "$APPS_DIR"
    cp "$DESKTOP_FILE" "$APPS_DIR/"
    
    echo "✅ Icône créée sur le bureau et dans le menu"
}

# Créer une icône simple
create_icon() {
    # Créer une icône SVG simple
    cat > "$INSTALL_DIR/icon.svg" << 'EOF'
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" rx="8" fill="#4F46E5"/>
  <text x="32" y="40" font-family="Arial" font-size="32" fill="white" text-anchor="middle">€</text>
</svg>
EOF

    # Convertir en PNG si possible
    if command -v convert &> /dev/null; then
        convert "$INSTALL_DIR/icon.svg" "$INSTALL_DIR/icon.png"
    else
        cp "$INSTALL_DIR/icon.svg" "$INSTALL_DIR/icon.png"
    fi
}

# Configuration initiale
setup_config() {
    echo "⚙️  Configuration initiale..."
    
    # Créer le fichier de configuration
    cat > "$INSTALL_DIR/server/.env" << 'EOF'
PORT=3333
NODE_ENV=production
DB_PATH=./data/database.sqlite
EOF

    # S'assurer que le répertoire de données existe
    mkdir -p "$INSTALL_DIR/server/data"
    mkdir -p "$INSTALL_DIR/server/backups"
    
    echo "✅ Configuration terminée"
}

# Exécution principale
main() {
    check_requirements
    install_app
    create_scripts
    create_icon
    create_desktop_icon
    setup_config
    
    echo ""
    echo "🎉 Installation terminée avec succès !"
    echo "======================================"
    echo "📍 Application installée dans : $INSTALL_DIR"
    echo "🖥️  Icône créée sur le bureau : MaComptaPerso"
    echo "🚀 Pour démarrer : Double-cliquez sur l'icône ou exécutez :"
    echo "   $INSTALL_DIR/start-app.sh"
    echo "🛑 Pour arrêter : $INSTALL_DIR/stop-app.sh"
    echo ""
    echo "🌐 L'application sera accessible sur : http://localhost:5173"
    echo "👤 Connexion par défaut : admin / password"
    echo ""
    echo "📖 Consultez le README.md pour plus d'informations"
}

main "$@"
```

### Utilisation du script d'installation

1. **Télécharger le script** :
```bash
curl -O https://raw.githubusercontent.com/votre-username/MaComptaPerso/main/install-macompta.sh
chmod +x install-macompta.sh
```

2. **Exécuter l'installation** :
```bash
./install-macompta.sh
```

## 🖥️ Utilisation quotidienne

### Démarrage de l'application

**Méthode 1 - Icône desktop (recommandée)** :
- Double-cliquez sur l'icône "Ma Compta Perso" sur le bureau
- L'application se lance automatiquement
- Le navigateur s'ouvre sur http://localhost:5173

**Méthode 2 - Ligne de commande** :
```bash
cd ~/MaComptaPerso
./start-app.sh
```

### Arrêt de l'application

```bash
cd ~/MaComptaPerso
./stop-app.sh
```

### Accès à l'application

- **URL** : http://localhost:5173
- **Utilisateur par défaut** : `admin`
- **Mot de passe par défaut** : `password`

⚠️ **Important** : Changez le mot de passe par défaut lors de la première connexion !

## 📁 Structure des fichiers

```
~/MaComptaPerso/
├── client/                 # Interface utilisateur
├── server/                 # Serveur backend
│   ├── data/              # Base de données SQLite
│   └── backups/           # Sauvegardes automatiques
├── start-app.sh           # Script de démarrage
├── stop-app.sh            # Script d'arrêt
├── app.log                # Fichiers de logs
├── icon.png               # Icône de l'application
└── README.md              # Documentation
```

## 🔧 Configuration avancée

### Changer les ports par défaut

Éditez le fichier `~/MaComptaPerso/server/.env` :
```env
PORT=3333                  # Port du serveur backend
```

Éditez le script `start-app.sh` pour changer le port du client (ligne avec `--port 5173`).

### Sauvegardes automatiques

L'application crée automatiquement des sauvegardes dans `server/backups/`.

Pour créer une sauvegarde manuelle :
```bash
cd ~/MaComptaPerso/server
cp data/database.sqlite backups/backup-$(date +%Y%m%d-%H%M%S).sqlite
```

## 🚨 Dépannage

### L'application ne démarre pas

1. **Vérifier les logs** :
```bash
tail -f ~/MaComptaPerso/app.log
```

2. **Vérifier les ports** :
```bash
netstat -tlnp | grep -E ':(3333|5173)'
```

3. **Redémarrer complètement** :
```bash
cd ~/MaComptaPerso
./stop-app.sh
sleep 5
./start-app.sh
```

### Erreur "Port déjà utilisé"

```bash
# Trouver et arrêter le processus utilisant le port
sudo lsof -ti:3333 | xargs kill -9
sudo lsof -ti:5173 | xargs kill -9
```

### Problèmes de permissions

```bash
cd ~/MaComptaPerso
chmod +x *.sh
chmod -R 755 server/data
```

## 🔄 Mise à jour

Pour mettre à jour l'application :

```bash
cd ~/MaComptaPerso
./stop-app.sh
git pull origin main
npm run install:all
cd client && npm run build && cd ..
./start-app.sh
```

## 🔐 Sécurité

### Recommandations importantes

1. **Changez le mot de passe par défaut** immédiatement
2. **Sauvegardez régulièrement** vos données
3. **N'exposez pas l'application** sur Internet sans sécurisation
4. **Utilisez un firewall** pour bloquer les ports 3333 et 5173 depuis l'extérieur

### Accès réseau local

Pour permettre l'accès depuis d'autres machines du réseau local, modifiez `start-app.sh` :
```bash
# Remplacer --host par --host 0.0.0.0
npx vite preview --port 5173 --host 0.0.0.0
```

⚠️ **Attention** : Cela rend l'application accessible depuis tout le réseau local.

## 📞 Support

### Logs et diagnostic

- **Logs de l'application** : `~/MaComptaPerso/app.log`
- **Base de données** : `~/MaComptaPerso/server/data/database.sqlite`
- **Configuration** : `~/MaComptaPerso/server/.env`

### Problèmes courants

| Problème | Solution |
|----------|----------|
| Page blanche | Vérifier que le serveur backend est démarré |
| Erreur de connexion | Vérifier les identifiants (admin/password) |
| Données perdues | Restaurer depuis `server/backups/` |
| Port occupé | Utiliser `./stop-app.sh` puis redémarrer |

---

**🎉 Votre application Ma Compta Perso est maintenant prête à l'emploi !**

Pour toute question, consultez la documentation dans le dossier `docs/` ou les fichiers README.md.
