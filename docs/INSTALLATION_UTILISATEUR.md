# 👤 Guide d'installation utilisateur - Ma Compta Perso

## 🎯 Installation par utilisateur

Ce guide explique comment installer **Ma Compta Perso** pour un utilisateur spécifique. Chaque utilisateur aura sa propre instance de l'application avec ses données privées.

## ✅ Avantages de cette approche

- **🔒 Données privées** : Chaque utilisateur a ses propres données comptables
- **🚫 Pas de conflits** : Plusieurs utilisateurs peuvent utiliser l'app simultanément
- **🎛️ Contrôle total** : Chacun gère ses sauvegardes et paramètres
- **🔧 Installation simple** : Pas besoin de droits administrateur

## 📋 Prérequis

### Logiciels requis (à installer par l'administrateur)
```bash
# Sur Fedora
sudo dnf install nodejs npm git

# Sur Ubuntu/Debian
sudo apt install nodejs npm git

# Vérification
node --version  # Doit être >= 16
npm --version
git --version
```

## 🚀 Installation automatique

### Méthode 1 : Script en une ligne (recommandée)

```bash
curl -s https://raw.githubusercontent.com/ppaperso/MaComptaPerso/main/deployment/install-macompta-no-sudo.sh | bash -s -- --repo https://github.com/ppaperso/MaComptaPerso.git
```

### Méthode 2 : Téléchargement puis exécution

```bash
# Télécharger le script
curl -O https://raw.githubusercontent.com/ppaperso/MaComptaPerso/main/deployment/install-macompta-no-sudo.sh

# Rendre exécutable
chmod +x install-macompta-no-sudo.sh

# Exécuter
./install-macompta-no-sudo.sh --repo https://github.com/ppaperso/MaComptaPerso.git
```

## 🔧 Installation manuelle

Si les scripts automatiques ne fonctionnent pas :

```bash
# 1. Cloner le projet
git clone https://github.com/ppaperso/MaComptaPerso.git ~/MaComptaPerso
cd ~/MaComptaPerso

# 2. Installer les dépendances
npm run install:all

# 3. Construire l'application
cd client && npm run build && cd ..

# 4. Configurer
mkdir -p server/data server/backups
echo "PORT=3333
NODE_ENV=production
DB_PATH=./data/database.sqlite" > server/.env

# 5. Créer les scripts de lancement
chmod +x deployment/start-production.sh deployment/stop-production.sh

# 6. Créer des liens symboliques (optionnel)
ln -sf deployment/start-production.sh start-app.sh
ln -sf deployment/stop-production.sh stop-app.sh
```

## 🖥️ Utilisation quotidienne

### Démarrer l'application

**Méthode 1 - Icône bureau** (si créée par le script automatique) :
- Double-cliquez sur "Ma Compta Perso"

**Méthode 2 - Ligne de commande** :
```bash
cd ~/MaComptaPerso
./start-app.sh
```

### Arrêter l'application

```bash
cd ~/MaComptaPerso
./stop-app.sh
```

### Accéder à l'application

- **URL** : http://localhost:5173
- **Identifiants par défaut** : 
  - Utilisateur : `admin`
  - Mot de passe : `password`

⚠️ **Important** : Changez le mot de passe lors de la première connexion !

## 📁 Structure des fichiers utilisateur

```
~/MaComptaPerso/
├── client/                    # Interface utilisateur
│   └── dist/                 # Version construite
├── server/                   # Serveur backend
│   ├── data/                # 🔒 VOS DONNÉES (base SQLite)
│   │   └── database.sqlite  # Base de données personnelle
│   ├── backups/             # 💾 Sauvegardes automatiques
│   └── .env                 # Configuration
├── start-app.sh             # 🚀 Démarrer l'application
├── stop-app.sh              # 🛑 Arrêter l'application
├── app.log                  # 📊 Fichiers de logs
└── icon.png                 # Icône de l'application
```

## 🔄 Gestion multi-utilisateurs

### Ports automatiques par utilisateur

Chaque utilisateur utilise des ports différents pour éviter les conflits :

- **Utilisateur 1** : http://localhost:5173
- **Utilisateur 2** : http://localhost:5174  
- **Utilisateur 3** : http://localhost:5175
- etc.

### Vérifier quel port utiliser

```bash
# Voir les ports utilisés
netstat -tlnp | grep :517

# Ou utiliser le script de vérification
cd ~/MaComptaPerso
./check-port.sh
```

## 💾 Sauvegardes

### Sauvegarde automatique

L'application crée automatiquement des sauvegardes dans `~/MaComptaPerso/server/backups/`

### Sauvegarde manuelle

```bash
cd ~/MaComptaPerso/server
cp data/database.sqlite backups/backup-$(date +%Y%m%d-%H%M%S).sqlite
```

### Restaurer une sauvegarde

```bash
cd ~/MaComptaPerso/server
# Arrêter l'application d'abord
../stop-app.sh

# Restaurer
cp backups/backup-YYYYMMDD-HHMMSS.sqlite data/database.sqlite

# Redémarrer
../start-app.sh
```

## 🔧 Maintenance

### Mise à jour de l'application

```bash
cd ~/MaComptaPerso
./stop-app.sh
git pull origin main
npm run install:all
cd client && npm run build && cd ..
./start-app.sh
```

### Vérifier les logs

```bash
# Logs en temps réel
tail -f ~/MaComptaPerso/app.log

# Logs récents
tail -50 ~/MaComptaPerso/app.log
```

### Nettoyer les anciens logs

```bash
cd ~/MaComptaPerso
# Garder seulement les 100 dernières lignes
tail -100 app.log > app.log.tmp && mv app.log.tmp app.log
```

## 🚨 Dépannage

### L'application ne démarre pas

1. **Vérifier les prérequis** :
```bash
node --version  # >= 16 requis
npm --version
```

2. **Vérifier les ports** :
```bash
netstat -tlnp | grep -E ':(3333|5173)'
```

3. **Vérifier les logs** :
```bash
tail -f ~/MaComptaPerso/app.log
```

### Port déjà utilisé

```bash
# Trouver qui utilise le port
lsof -ti:5173

# Arrêter proprement
cd ~/MaComptaPerso
./stop-app.sh

# Forcer l'arrêt si nécessaire
pkill -f "vite.*preview"
pkill -f "node.*index.js"
```

### Données corrompues

```bash
# Restaurer depuis une sauvegarde
cd ~/MaComptaPerso/server
ls -la backups/  # Voir les sauvegardes disponibles
cp backups/backup-YYYYMMDD-HHMMSS.sqlite data/database.sqlite
```

## 🔐 Sécurité

### Recommandations importantes

1. **Changez le mot de passe par défaut** immédiatement
2. **Sauvegardez régulièrement** vos données
3. **Ne partagez pas** vos identifiants
4. **Vérifiez les permissions** de vos fichiers :
```bash
chmod 700 ~/MaComptaPerso/server/data
chmod 600 ~/MaComptaPerso/server/data/database.sqlite
```

### Accès réseau

Par défaut, l'application n'est accessible que depuis votre machine (`localhost`). 
Pour un accès réseau, consultez la documentation avancée.

## 📞 Support

### Informations de diagnostic

```bash
# Informations système
echo "Utilisateur: $(whoami)"
echo "Répertoire: $(pwd)"
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "Git: $(git --version)"

# État de l'application
cd ~/MaComptaPerso
ls -la
ps aux | grep -E "(node|vite)" | grep -v grep
```

### Problèmes courants

| Problème | Solution |
|----------|----------|
| Page blanche | Vérifier que le serveur backend est démarré |
| Erreur de connexion | Vérifier les identifiants (admin/password) |
| Port occupé | Utiliser `./stop-app.sh` puis redémarrer |
| Données perdues | Restaurer depuis `server/backups/` |

---

**🎉 Votre installation personnelle de Ma Compta Perso est prête !**

Chaque utilisateur du PC peut suivre ce guide pour avoir sa propre instance privée de l'application.
