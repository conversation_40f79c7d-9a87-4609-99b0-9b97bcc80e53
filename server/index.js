const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const bcrypt = require('bcrypt');
const fs = require('fs');
const path = require('path');
const { db, getAccount, createOrUpdateAccount, getUnreconciledTransactions, reconcileTransaction, isTransactionReconciled, getPaymentMethods, insertPaymentMethod, updatePaymentMethod, deletePaymentMethod, getPaymentMethod, getRecipients, insertRecipient, updateRecipient, deleteRecipient, getRecipient, getBackupHistory, deleteBackupLog } = require('./db');

const app = express();
app.use(cors({
  origin: 'http://localhost:5173', // URL du client React
  credentials: true
}));
app.use(bodyParser.json());

// Stockage temporaire des sessions en mémoire (à remplacer par une vraie solution en production)
const sessions = new Map();

// Middleware d'authentification
const requireAuth = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token || !sessions.has(token)) {
    return res.status(401).json({ error: 'Non authentifié' });
  }
  req.user = sessions.get(token);
  next();
};

// Helper functions pour sqlite3 (API asynchrone)
const getUserByUsername = (username, callback) => {
  db.get('SELECT id, username, password_hash FROM users WHERE username = ?', [username], callback);
};

const createUser = (username, passwordHash, callback) => {
  db.run('INSERT INTO users (username, password_hash) VALUES (?, ?)', [username, passwordHash], callback);
};

const insertCategory = (name, userId, callback) => {
  db.run('INSERT INTO categories (name, user_id) VALUES (?, ?)', [name, userId], callback);
};

const updateCategory = (id, name, userId, callback) => {
  db.run('UPDATE categories SET name = ? WHERE id = ? AND user_id = ?', [name, id, userId], callback);
};

const deleteCategory = (id, userId, callback) => {
  db.run('DELETE FROM categories WHERE id = ? AND user_id = ?', [id, userId], callback);
};

const getCategories = (userId, callback) => {
  db.all('SELECT id, name FROM categories WHERE user_id = ? ORDER BY name', [userId], callback);
};

const insertTransaction = (amount, type, categoryId, description, dateIso, userId, paymentMethodId, checkNumber, recipientId, callback) => {
  db.run('INSERT INTO transactions (amount, type, category_id, description, date_iso, user_id, payment_method_id, check_number, recipient_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
    [amount, type, categoryId, description, dateIso, userId, paymentMethodId, checkNumber, recipientId], callback);
};

const getTransactions = (userId, callback) => {
  db.all(`SELECT t.id, t.amount, t.type, t.description, t.date_iso, c.id as category_id, c.name as category_name,
    t.is_reconciled, t.reconciled_date, t.payment_method_id, t.check_number, p.name as payment_method_name, p.requires_check_number,
    t.recipient_id, r.name as recipient_name
    FROM transactions t
    LEFT JOIN categories c ON t.category_id = c.id
    LEFT JOIN payment_methods p ON t.payment_method_id = p.id
    LEFT JOIN recipients r ON t.recipient_id = r.id
    WHERE t.user_id = ? ORDER BY date_iso DESC`, [userId], callback);
};

const getTransaction = (id, userId, callback) => {
  db.get(`SELECT t.id, t.amount, t.type, t.description, t.date_iso, c.id as category_id, c.name as category_name,
    t.is_reconciled, t.reconciled_date, t.payment_method_id, t.check_number, p.name as payment_method_name, p.requires_check_number,
    t.recipient_id, r.name as recipient_name
    FROM transactions t
    LEFT JOIN categories c ON t.category_id = c.id
    LEFT JOIN payment_methods p ON t.payment_method_id = p.id
    LEFT JOIN recipients r ON t.recipient_id = r.id
    WHERE t.id = ? AND t.user_id = ?`, [id, userId], callback);
};

const updateTransaction = (id, amount, type, categoryId, description, dateIso, userId, paymentMethodId, checkNumber, recipientId, callback) => {
  db.run('UPDATE transactions SET amount = ?, type = ?, category_id = ?, description = ?, date_iso = ?, payment_method_id = ?, check_number = ?, recipient_id = ? WHERE id = ? AND user_id = ?',
    [amount, type, categoryId, description, dateIso, paymentMethodId, checkNumber, recipientId, id, userId], callback);
};

const deleteTransaction = (id, userId, callback) => {
  db.run('DELETE FROM transactions WHERE id = ? AND user_id = ?', [id, userId], callback);
};

const getBalance = (userId, callback) => {
  // Récupérer les totaux des transactions ET le solde initial du compte
  db.get(`SELECT
    COALESCE(SUM(CASE WHEN t.type='in' THEN t.amount ELSE 0 END),0) as total_in,
    COALESCE(SUM(CASE WHEN t.type='out' THEN t.amount ELSE 0 END),0) as total_out,
    COALESCE(a.initial_balance, 0) as initial_balance
  FROM transactions t
  LEFT JOIN accounts a ON a.user_id = t.user_id
  WHERE t.user_id = ?
  GROUP BY a.initial_balance`, [userId], (err, row) => {
    if (err) return callback(err);

    // Si aucune transaction n'existe, récupérer quand même le solde initial
    if (!row) {
      db.get('SELECT COALESCE(initial_balance, 0) as initial_balance FROM accounts WHERE user_id = ?', [userId], (err, accountRow) => {
        if (err) return callback(err);

        const result = {
          total_in: 0,
          total_out: 0,
          initial_balance: accountRow?.initial_balance || 0
        };
        callback(null, result);
      });
    } else {
      callback(null, row);
    }
  });
};

const createBackupLog = (userId, status, transactionsCount, categoriesCount, backupFilePath, backupFileSize, errorMessage, callback) => {
  db.run('INSERT INTO backup_logs (user_id, status, transactions_count, categories_count, backup_file_path, backup_file_size, error_message) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [userId, status, transactionsCount, categoriesCount, backupFilePath, backupFileSize, errorMessage], callback);
};

// Fonction pour marquer qu'une table a été modifiée
const markDataChanged = (userId, tableName, callback) => {
  db.run('INSERT OR REPLACE INTO data_changes (user_id, table_name, last_modified) VALUES (?, ?, CURRENT_TIMESTAMP)',
    [userId, tableName], callback);
};

// Fonction pour vérifier si des données ont changé depuis la dernière sauvegarde
const hasDataChanged = (userId, callback) => {
  // D'abord, récupérer la date de la dernière sauvegarde réussie
  db.get(`
    SELECT backup_date
    FROM backup_logs
    WHERE user_id = ? AND status = 'success'
    ORDER BY backup_date DESC
    LIMIT 1
  `, [userId], (err, lastBackup) => {
    if (err) return callback(err, false);

    // Si pas de sauvegarde précédente, il faut sauvegarder
    if (!lastBackup) {
      return callback(null, true);
    }

    // Ensuite, vérifier s'il y a des changements depuis cette date
    db.get(`
      SELECT COUNT(*) as changes_count
      FROM data_changes
      WHERE user_id = ? AND last_modified > ?
    `, [userId, lastBackup.backup_date], (err, result) => {
      if (err) return callback(err, false);

      // S'il y a des changements depuis la dernière sauvegarde
      const hasChanged = result.changes_count > 0;
      callback(null, hasChanged);
    });
  });
};

// Fonction pour créer une sauvegarde physique de la base de données
const createPhysicalBackup = (userId, callback) => {
  const now = new Date();
  const timestamp = now.toISOString().replace(/[:.]/g, '-').replace('T', '_').split('.')[0];
  const backupFileName = `spc_backup_${timestamp}.db`;
  const backupPath = path.join(__dirname, 'backups', backupFileName);
  const sourcePath = path.join(__dirname, 'data', 'spc.db');

  // Copier le fichier de base de données
  fs.copyFile(sourcePath, backupPath, (err) => {
    if (err) {
      return callback(err, null);
    }

    // Obtenir la taille du fichier de sauvegarde
    fs.stat(backupPath, (err, stats) => {
      if (err) {
        return callback(err, null);
      }

      callback(null, {
        filePath: backupPath,
        fileName: backupFileName,
        fileSize: stats.size
      });
    });
  });
};

// Fonction pour nettoyer les anciennes sauvegardes (garder les 10 dernières)
const cleanupOldBackups = (callback) => {
  const backupsDir = path.join(__dirname, 'backups');

  fs.readdir(backupsDir, (err, files) => {
    if (err) return callback(err);

    // Filtrer les fichiers de sauvegarde et les trier par date (plus récent en premier)
    const backupFiles = files
      .filter(file => file.startsWith('spc_backup_') && file.endsWith('.db'))
      .sort((a, b) => b.localeCompare(a)); // Tri décroissant par nom (timestamp)

    // Garder les 10 plus récents, supprimer les autres
    const filesToDelete = backupFiles.slice(10);

    let deletedCount = 0;
    if (filesToDelete.length === 0) {
      return callback(null, 0);
    }

    filesToDelete.forEach(file => {
      const filePath = path.join(backupsDir, file);
      fs.unlink(filePath, (err) => {
        if (err) console.error('Erreur lors de la suppression de', file, ':', err);
        deletedCount++;
        if (deletedCount === filesToDelete.length) {
          callback(null, filesToDelete.length);
        }
      });
    });
  });
};

const getLastBackupLog = (userId, callback) => {
  db.get('SELECT * FROM backup_logs WHERE user_id = ? ORDER BY backup_date DESC LIMIT 1', [userId], callback);
};

const updateUserProfile = (userId, username, callback) => {
  db.run('UPDATE users SET username = ? WHERE id = ?', [username, userId], callback);
};

const updateUserPassword = (userId, passwordHash, callback) => {
  db.run('UPDATE users SET password_hash = ? WHERE id = ?', [passwordHash, userId], callback);
};

const deleteUserAndData = (userId, callback) => {
  // Supprimer toutes les données de l'utilisateur dans l'ordre correct (contraintes FK)
  db.serialize(() => {
    db.run('DELETE FROM backup_logs WHERE user_id = ?', [userId]);
    db.run('DELETE FROM transactions WHERE user_id = ?', [userId]);
    db.run('DELETE FROM categories WHERE user_id = ?', [userId]);
    db.run('DELETE FROM users WHERE id = ?', [userId], callback);
  });
};

// Routes d'authentification
app.post('/api/login', async (req, res) => {
  const { username, password } = req.body;
  if (!username || !password) {
    return res.status(400).json({ error: 'Nom d\'utilisateur et mot de passe requis' });
  }

  getUserByUsername(username, async (err, user) => {
    if (err) {
      console.error('Erreur lors de la recherche de l\'utilisateur:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    if (!user) {
      return res.status(401).json({ error: 'Identifiants invalides' });
    }

    try {
      // Vérification du mot de passe avec bcrypt
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        return res.status(401).json({ error: 'Identifiants invalides' });
      }

      // Créer un token simple (à remplacer par JWT en production)
      const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
      sessions.set(token, { id: user.id, username: user.username });

      res.json({ success: true, username: user.username, token });
    } catch (bcryptError) {
      console.error('Erreur lors de la vérification du mot de passe:', bcryptError);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
  });
});

app.post('/api/register', async (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ error: 'Nom d\'utilisateur et mot de passe requis' });
  }

  if (username.length < 3) {
    return res.status(400).json({ error: 'Le nom d\'utilisateur doit contenir au moins 3 caractères' });
  }

  if (password.length < 6) {
    return res.status(400).json({ error: 'Le mot de passe doit contenir au moins 6 caractères' });
  }

  try {
    // Vérifier si l'utilisateur existe déjà
    getUserByUsername(username, async (err, existingUser) => {
      if (err) {
        console.error('Erreur lors de la vérification de l\'utilisateur:', err);
        return res.status(500).json({ error: 'Erreur serveur' });
      }

      if (existingUser) {
        return res.status(400).json({ error: 'Ce nom d\'utilisateur est déjà pris' });
      }

      try {
        // Hasher le mot de passe
        const saltRounds = 10;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        // Créer l'utilisateur
        createUser(username, passwordHash, function(err) {
          if (err) {
            console.error('Erreur lors de la création de l\'utilisateur:', err);
            return res.status(500).json({ error: 'Erreur lors de la création du compte' });
          }

          const userId = this.lastID;

          // Créer les catégories par défaut pour le nouvel utilisateur
          const defaultCategories = [
            '🏠 Logement',
            '🚗 Transport',
            '🍽️ Alimentation',
            '📱 Téléphone',
            '💡 Électricité',
            '💧 Eau',
            '🌐 Internet',
            '🏥 Santé',
            '👕 Vêtements',
            '🎬 Loisirs',
            '📚 Éducation',
            '🎁 Cadeaux',
            '💰 Épargne',
            '🏪 Courses',
            '⛽ Carburant',
            '🔧 Réparations',
            '📄 Assurances',
            '🏦 Banque',
            '💼 Travail',
            '🎯 Autres'
          ];

          let categoriesCreated = 0;
          const totalCategories = defaultCategories.length;

          defaultCategories.forEach(categoryName => {
            insertCategory(categoryName, userId, (err) => {
              categoriesCreated++;
              if (err) {
                console.error('Erreur lors de la création de la catégorie:', categoryName, err);
              }

              // Quand toutes les catégories sont traitées, créer les destinataires par défaut
              if (categoriesCreated === totalCategories) {
                // Créer les destinataires par défaut
                const defaultRecipients = [
                  '👨 Papa',
                  '👩 Maman',
                  '👧 Enfant 1',
                  '👦 Enfant 2',
                  '🐕 Animal de compagnie',
                  '👨‍👩‍👧‍👦 Toute la famille',
                  '🏠 Maison/Logement',
                  '🎯 Autre'
                ];

                let recipientsCreated = 0;
                const totalRecipients = defaultRecipients.length;

                defaultRecipients.forEach(recipientName => {
                  insertRecipient(recipientName, userId, (err) => {
                    recipientsCreated++;
                    if (err) {
                      console.error('Erreur lors de la création du destinataire:', recipientName, err);
                    }

                    // Quand tous les destinataires sont traités, renvoyer la réponse
                    if (recipientsCreated === totalRecipients) {
                      res.status(201).json({
                        success: true,
                        message: 'Compte créé avec succès',
                        username: username
                      });
                    }
                  });
                });
              }
            });
          });
        });
      } catch (hashError) {
        console.error('Erreur lors du hachage du mot de passe:', hashError);
        return res.status(500).json({ error: 'Erreur lors de la création du compte' });
      }
    });
  } catch (error) {
    console.error('Erreur lors de l\'inscription:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
});

app.post('/api/logout', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token) {
    sessions.delete(token);
  }
  res.json({ success: true });
});

app.get('/api/me', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token || !sessions.has(token)) {
    return res.status(401).json({ error: 'Non authentifié' });
  }
  const user = sessions.get(token);
  res.json({
    authenticated: true,
    username: user.username,
    userId: user.id
  });
});

// Routes protégées
app.get('/api/categories', requireAuth, (req, res) => {
  getCategories(req.user.id, (err, rows) => {
    if (err) {
      console.error('Erreur lors de la récupération des catégories:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.json(rows || []);
  });
});

app.post('/api/categories', requireAuth, (req, res) => {
  const { name } = req.body;
  if (!name || !name.trim()) return res.status(400).json({ error: 'Nom requis' });

  insertCategory(name.trim(), req.user.id, function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT') {
        return res.status(400).json({ error: 'Cette catégorie existe déjà' });
      }
      console.error('Erreur lors de l\'insertion de la catégorie:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    // Marquer que les données ont changé
    markDataChanged(req.user.id, 'categories', () => {});

    res.status(201).json({ id: this.lastID, ok: true });
  });
});

app.put('/api/categories/:id', requireAuth, (req, res) => {
  const { name } = req.body;
  const id = Number(req.params.id);

  if (!name || !name.trim()) return res.status(400).json({ error: 'Nom requis' });
  if (isNaN(id)) return res.status(400).json({ error: 'ID invalide' });

  updateCategory(id, name.trim(), req.user.id, function(err) {
    if (err) {
      console.error('Erreur lors de la mise à jour de la catégorie:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Catégorie non trouvée' });
    }

    // Marquer que les données ont changé
    markDataChanged(req.user.id, 'categories', () => {});

    res.json({ ok: true });
  });
});

app.delete('/api/categories/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) return res.status(400).json({ error: 'ID invalide' });

  deleteCategory(id, req.user.id, function(err) {
    if (err) {
      console.error('Erreur lors de la suppression de la catégorie:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Catégorie non trouvée' });
    }

    // Marquer que les données ont changé
    markDataChanged(req.user.id, 'categories', () => {});

    res.json({ ok: true });
  });
});

// Route pour initialiser les catégories par défaut
app.post('/api/categories/init-defaults', requireAuth, (req, res) => {
  const defaultCategories = [
    '🏠 Logement',
    '🚗 Transport',
    '🍽️ Alimentation',
    '📱 Téléphone',
    '💡 Électricité',
    '💧 Eau',
    '🌐 Internet',
    '🏥 Santé',
    '👕 Vêtements',
    '🎬 Loisirs',
    '📚 Éducation',
    '🎁 Cadeaux',
    '💰 Épargne',
    '🏪 Courses',
    '⛽ Carburant',
    '🔧 Réparations',
    '📄 Assurances',
    '🏦 Banque',
    '💼 Travail',
    '🎯 Autres'
  ];

  let completed = 0;
  let errors = 0;

  defaultCategories.forEach(categoryName => {
    insertCategory(categoryName, req.user.id, (err) => {
      completed++;
      if (err) {
        errors++;
        console.error('Erreur lors de la création de la catégorie:', categoryName, err);
      }

      // Quand toutes les catégories ont été traitées
      if (completed === defaultCategories.length) {
        const created = defaultCategories.length - errors;
        res.json({
          ok: true,
          message: `${created} catégories créées avec succès`,
          created: created,
          errors: errors
        });
      }
    });
  });
});

// Routes pour les modes de paiement
app.get('/api/payment-methods', requireAuth, (req, res) => {
  getPaymentMethods(req.user.id, (err, rows) => {
    if (err) {
      console.error('Erreur lors de la récupération des modes de paiement:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.json(rows || []);
  });
});

app.post('/api/payment-methods', requireAuth, (req, res) => {
  const { name, requires_check_number } = req.body;
  if (!name || !name.trim()) {
    return res.status(400).json({ error: 'Nom du mode de paiement requis' });
  }

  insertPaymentMethod(name.trim(), req.user.id, requires_check_number, function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return res.status(409).json({ error: 'Ce mode de paiement existe déjà' });
      }
      console.error('Erreur lors de la création du mode de paiement:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.status(201).json({ id: this.lastID });
  });
});

app.get('/api/payment-methods/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }

  getPaymentMethod(id, req.user.id, (err, row) => {
    if (err) {
      console.error('Erreur lors de la récupération du mode de paiement:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (!row) {
      return res.status(404).json({ error: 'Mode de paiement non trouvé' });
    }
    res.json(row);
  });
});

app.put('/api/payment-methods/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  const { name, requires_check_number } = req.body;

  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }
  if (!name || !name.trim()) {
    return res.status(400).json({ error: 'Nom du mode de paiement requis' });
  }

  updatePaymentMethod(id, name.trim(), req.user.id, requires_check_number, function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return res.status(409).json({ error: 'Ce mode de paiement existe déjà' });
      }
      console.error('Erreur lors de la mise à jour du mode de paiement:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Mode de paiement non trouvé' });
    }
    res.json({ ok: true });
  });
});

app.delete('/api/payment-methods/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }

  deletePaymentMethod(id, req.user.id, function(err) {
    if (err) {
      console.error('Erreur lors de la suppression du mode de paiement:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Mode de paiement non trouvé' });
    }
    res.json({ ok: true });
  });
});

// Routes pour les destinataires
app.get('/api/recipients', requireAuth, (req, res) => {
  getRecipients(req.user.id, (err, rows) => {
    if (err) {
      console.error('Erreur lors de la récupération des destinataires:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.json(rows || []);
  });
});

app.post('/api/recipients', requireAuth, (req, res) => {
  const { name } = req.body;
  if (!name || !name.trim()) {
    return res.status(400).json({ error: 'Nom du destinataire requis' });
  }

  insertRecipient(name.trim(), req.user.id, function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return res.status(409).json({ error: 'Ce destinataire existe déjà' });
      }
      console.error('Erreur lors de la création du destinataire:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.status(201).json({ id: this.lastID });
  });
});

app.get('/api/recipients/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }

  getRecipient(id, req.user.id, (err, row) => {
    if (err) {
      console.error('Erreur lors de la récupération du destinataire:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (!row) {
      return res.status(404).json({ error: 'Destinataire non trouvé' });
    }
    res.json(row);
  });
});

app.put('/api/recipients/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  const { name } = req.body;

  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }
  if (!name || !name.trim()) {
    return res.status(400).json({ error: 'Nom du destinataire requis' });
  }

  updateRecipient(id, name.trim(), req.user.id, function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return res.status(409).json({ error: 'Ce destinataire existe déjà' });
      }
      console.error('Erreur lors de la mise à jour du destinataire:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Destinataire non trouvé' });
    }
    res.json({ ok: true });
  });
});

app.delete('/api/recipients/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }

  deleteRecipient(id, req.user.id, function(err) {
    if (err) {
      console.error('Erreur lors de la suppression du destinataire:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Destinataire non trouvé' });
    }
    res.json({ ok: true });
  });
});

app.get('/api/transactions', requireAuth, (req, res) => {
  getTransactions(req.user.id, (err, rows) => {
    if (err) {
      console.error('Erreur lors de la récupération des transactions:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.json(rows || []);
  });
});

// Routes pour le pointage des transactions (AVANT les routes avec paramètres)
app.get('/api/transactions/unreconciled', requireAuth, (req, res) => {
  getUnreconciledTransactions(req.user.id, (err, rows) => {
    if (err) {
      console.error('Erreur lors de la récupération des transactions non pointées:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    res.json(rows || []);
  });
});

app.post('/api/transactions', requireAuth, (req, res) => {
  const { amount, type, category_id, description, date_iso, payment_method_id, check_number, recipient_id } = req.body;
  if (typeof amount !== 'number' || !['in','out'].includes(type)) {
    return res.status(400).json({ error: 'Données invalides' });
  }

  const dateISO = date_iso || new Date().toISOString();
  const catId = category_id || null;
  const paymentMethodId = payment_method_id || null;
  const checkNum = check_number || null;
  const recipientId = recipient_id || null;

  insertTransaction(amount, type, catId, description || null, dateISO, req.user.id, paymentMethodId, checkNum, recipientId, function(err) {
    if (err) {
      console.error('Erreur lors de l\'insertion de la transaction:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    // Marquer que les données ont changé
    markDataChanged(req.user.id, 'transactions', () => {});

    res.status(201).json({ id: this.lastID });
  });
});

app.get('/api/transactions/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }

  getTransaction(id, req.user.id, (err, row) => {
    if (err) {
      console.error('Erreur lors de la récupération de la transaction:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (!row) {
      return res.status(404).json({ error: 'Transaction non trouvée' });
    }
    res.json(row);
  });
});

app.put('/api/transactions/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  const { amount, type, category_id, description, date_iso, payment_method_id, check_number, recipient_id } = req.body;

  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }
  if (typeof amount !== 'number' || !['in','out'].includes(type)) {
    return res.status(400).json({ error: 'Données invalides' });
  }

  // Vérifier si la transaction est pointée avant de la modifier
  isTransactionReconciled(id, req.user.id, (err, reconciled) => {
    if (err) {
      console.error('Erreur lors de la vérification du pointage:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    if (reconciled) {
      return res.status(403).json({ error: 'Impossible de modifier une transaction pointée' });
    }

    const dateISO = date_iso || new Date().toISOString();
    const catId = category_id || null;
    const paymentMethodId = payment_method_id || null;
    const checkNum = check_number || null;
    const recipientId = recipient_id || null;

    updateTransaction(id, amount, type, catId, description || null, dateISO, req.user.id, paymentMethodId, checkNum, recipientId, function(err) {
      if (err) {
        console.error('Erreur lors de la mise à jour de la transaction:', err);
        return res.status(500).json({ error: 'Erreur serveur' });
      }
      if (this.changes === 0) {
        return res.status(404).json({ error: 'Transaction non trouvée' });
      }

      // Marquer que les données ont changé
      markDataChanged(req.user.id, 'transactions', () => {});

      res.json({ ok: true });
    });
  });
});

app.delete('/api/transactions/:id', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }

  // Vérifier si la transaction est pointée avant de la supprimer
  isTransactionReconciled(id, req.user.id, (err, reconciled) => {
    if (err) {
      console.error('Erreur lors de la vérification du pointage:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    if (reconciled) {
      return res.status(403).json({ error: 'Impossible de supprimer une transaction pointée' });
    }

    deleteTransaction(id, req.user.id, (err) => {
      if (err) {
        console.error('Erreur lors de la suppression de la transaction:', err);
        return res.status(500).json({ error: 'Erreur serveur' });
      }

      // Marquer que les données ont changé
      markDataChanged(req.user.id, 'transactions', () => {});

      res.json({ ok: true });
    });
  });
});

app.put('/api/transactions/:id/reconcile', requireAuth, (req, res) => {
  const id = Number(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ error: 'ID invalide' });
  }

  reconcileTransaction(id, req.user.id, function(err) {
    if (err) {
      console.error('Erreur lors du pointage de la transaction:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Transaction non trouvée' });
    }

    // Marquer que les données ont changé
    markDataChanged(req.user.id, 'transactions', () => {});

    res.json({ ok: true });
  });
});

app.get('/api/balance', requireAuth, (req, res) => {
  getBalance(req.user.id, (err, row) => {
    if (err) {
      console.error('Erreur lors du calcul du solde:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    const totalIn = row?.total_in || 0;
    const totalOut = row?.total_out || 0;
    const initialBalance = row?.initial_balance || 0;

    const balance = {
      total_in: totalIn,
      total_out: totalOut,
      initial_balance: initialBalance,
      balance: initialBalance + totalIn - totalOut
    };

    res.json(balance);
  });
});

// Routes de gestion du profil utilisateur
app.put('/api/profile', requireAuth, async (req, res) => {
  const { username } = req.body;
  const userId = req.user.id;

  if (!username || username.length < 3) {
    return res.status(400).json({ error: 'Le nom d\'utilisateur doit contenir au moins 3 caractères' });
  }

  // Vérifier si le nom d'utilisateur est déjà pris par un autre utilisateur
  getUserByUsername(username, (err, existingUser) => {
    if (err) {
      console.error('Erreur lors de la vérification du nom d\'utilisateur:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    if (existingUser && existingUser.id !== userId) {
      return res.status(400).json({ error: 'Ce nom d\'utilisateur est déjà pris' });
    }

    updateUserProfile(userId, username, function(err) {
      if (err) {
        console.error('Erreur lors de la mise à jour du profil:', err);
        return res.status(500).json({ error: 'Erreur lors de la mise à jour du profil' });
      }

      // Mettre à jour la session
      const token = req.headers.authorization?.replace('Bearer ', '');
      if (token && sessions.has(token)) {
        sessions.set(token, { id: userId, username: username });
      }

      res.json({ success: true, message: 'Profil mis à jour avec succès' });
    });
  });
});

app.put('/api/profile/password', requireAuth, async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({ error: 'Mot de passe actuel et nouveau mot de passe requis' });
  }

  if (newPassword.length < 6) {
    return res.status(400).json({ error: 'Le nouveau mot de passe doit contenir au moins 6 caractères' });
  }

  try {
    // Récupérer l'utilisateur pour vérifier le mot de passe actuel
    getUserByUsername(req.user.username, async (err, user) => {
      if (err) {
        console.error('Erreur lors de la récupération de l\'utilisateur:', err);
        return res.status(500).json({ error: 'Erreur serveur' });
      }

      if (!user) {
        return res.status(404).json({ error: 'Utilisateur non trouvé' });
      }

      // Vérifier le mot de passe actuel
      const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
      if (!isValidPassword) {
        return res.status(400).json({ error: 'Mot de passe actuel incorrect' });
      }

      // Hasher le nouveau mot de passe
      const saltRounds = 10;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      updateUserPassword(userId, newPasswordHash, function(err) {
        if (err) {
          console.error('Erreur lors de la mise à jour du mot de passe:', err);
          return res.status(500).json({ error: 'Erreur lors de la mise à jour du mot de passe' });
        }

        res.json({ success: true, message: 'Mot de passe modifié avec succès' });
      });
    });
  } catch (error) {
    console.error('Erreur lors du changement de mot de passe:', error);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
});

app.delete('/api/profile', requireAuth, (req, res) => {
  const userId = req.user.id;

  deleteUserAndData(userId, function(err) {
    if (err) {
      console.error('Erreur lors de la suppression du compte:', err);
      return res.status(500).json({ error: 'Erreur lors de la suppression du compte' });
    }

    // Supprimer la session
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (token) {
      sessions.delete(token);
    }

    res.json({ success: true, message: 'Compte supprimé avec succès' });
  });
});

// Routes de gestion des comptes
app.get('/api/account', requireAuth, (req, res) => {
  const userId = req.user.id;

  getAccount(userId, (err, account) => {
    if (err) {
      console.error('Erreur lors de la récupération du compte:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    if (!account) {
      // Créer un compte par défaut si aucun n'existe
      const defaultAccount = {
        account_name: 'Mon Compte Principal',
        iban: '',
        bank_address: '',
        initial_balance: 0,
        initial_balance_date: new Date().toISOString()
      };

      createOrUpdateAccount(userId, defaultAccount, function(err) {
        if (err) {
          console.error('Erreur lors de la création du compte par défaut:', err);
          return res.status(500).json({ error: 'Erreur serveur' });
        }

        // Récupérer le compte nouvellement créé
        getAccount(userId, (err, newAccount) => {
          if (err) {
            console.error('Erreur lors de la récupération du nouveau compte:', err);
            return res.status(500).json({ error: 'Erreur serveur' });
          }
          res.json(newAccount);
        });
      });
    } else {
      res.json(account);
    }
  });
});

app.put('/api/account', requireAuth, (req, res) => {
  const userId = req.user.id;
  const { account_name, iban, bank_address, initial_balance, initial_balance_date } = req.body;

  // Validation des données
  if (!account_name || account_name.trim().length === 0) {
    return res.status(400).json({ error: 'Le nom du compte est requis' });
  }

  if (initial_balance !== undefined && (isNaN(initial_balance) || initial_balance === null)) {
    return res.status(400).json({ error: 'Le solde initial doit être un nombre valide' });
  }

  const accountData = {
    account_name: account_name.trim(),
    iban: iban ? iban.trim() : null,
    bank_address: bank_address ? bank_address.trim() : null,
    initial_balance: initial_balance || 0,
    initial_balance_date: initial_balance_date || new Date().toISOString()
  };

  createOrUpdateAccount(userId, accountData, function(err) {
    if (err) {
      console.error('Erreur lors de la mise à jour du compte:', err);
      return res.status(500).json({ error: 'Erreur lors de la mise à jour du compte' });
    }

    // Marquer que les données ont changé
    markDataChanged(userId, 'accounts', () => {});

    res.json({ success: true, message: 'Compte mis à jour avec succès' });
  });
});

// Routes de sauvegarde
app.post('/api/backup', requireAuth, (req, res) => {
  const userId = req.user.id;
  console.log('💾 Démarrage de la sauvegarde pour l\'utilisateur:', userId);

  // Vérifier si des données ont changé depuis la dernière sauvegarde
  hasDataChanged(userId, (err, hasChanged) => {
    if (err) {
      console.error('Erreur lors de la vérification des changements:', err);
      createBackupLog(userId, 'error', 0, 0, null, 0, err.message, () => {});
      return res.status(500).json({ error: 'Erreur lors de la vérification des changements' });
    }

    if (!hasChanged) {
      console.log('ℹ️  Aucun changement détecté, sauvegarde ignorée');
      return res.json({
        success: true,
        message: 'Aucun changement détecté, sauvegarde non nécessaire',
        skipped: true
      });
    }

    console.log('✅ Changements détectés, création de la sauvegarde...');

    // Compter les transactions et catégories de l'utilisateur
    db.get('SELECT COUNT(*) as count FROM transactions WHERE user_id = ?', [userId], (err, transactionResult) => {
      if (err) {
        console.error('Erreur lors du comptage des transactions:', err);
        createBackupLog(userId, 'error', 0, 0, null, 0, err.message, () => {});
        return res.status(500).json({ error: 'Erreur lors de la sauvegarde' });
      }

      db.get('SELECT COUNT(*) as count FROM categories WHERE user_id = ?', [userId], (err, categoryResult) => {
        if (err) {
          console.error('Erreur lors du comptage des catégories:', err);
          createBackupLog(userId, 'error', 0, 0, null, 0, err.message, () => {});
          return res.status(500).json({ error: 'Erreur lors de la sauvegarde' });
        }

        const transactionsCount = transactionResult.count;
        const categoriesCount = categoryResult.count;

        // Créer la sauvegarde physique
        createPhysicalBackup(userId, (err, backupInfo) => {
          if (err) {
            console.error('Erreur lors de la création de la sauvegarde physique:', err);
            createBackupLog(userId, 'error', transactionsCount, categoriesCount, null, 0, err.message, () => {});
            return res.status(500).json({ error: 'Erreur lors de la création de la sauvegarde' });
          }

          console.log('📁 Sauvegarde créée:', backupInfo.fileName);

          // Créer le log de sauvegarde avec les informations du fichier
          createBackupLog(userId, 'success', transactionsCount, categoriesCount, backupInfo.filePath, backupInfo.fileSize, null, function(err) {
            if (err) {
              console.error('Erreur lors de la création du log de sauvegarde:', err);
              return res.status(500).json({ error: 'Erreur lors de la sauvegarde' });
            }

            // Nettoyer les anciennes sauvegardes
            cleanupOldBackups((err, deletedCount) => {
              if (err) {
                console.error('Erreur lors du nettoyage des anciennes sauvegardes:', err);
              } else if (deletedCount > 0) {
                console.log(`🗑️  ${deletedCount} anciennes sauvegardes supprimées`);
              }
            });

            res.json({
              success: true,
              message: 'Sauvegarde effectuée avec succès',
              backup_id: this.lastID,
              transactions_count: transactionsCount,
              categories_count: categoriesCount,
              backup_file: backupInfo.fileName,
              backup_size: backupInfo.fileSize,
              backup_date: new Date().toISOString()
            });
          });
        });
      });
    });
  });
});

app.get('/api/backup/last', requireAuth, (req, res) => {
  console.log('🔍 Récupération du dernier backup pour l\'utilisateur:', req.user.id);
  getLastBackupLog(req.user.id, (err, backup) => {
    if (err) {
      console.error('❌ Erreur lors de la récupération du dernier backup:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    console.log('📊 Backup trouvé:', backup);
    if (!backup) {
      console.log('ℹ️  Aucun backup trouvé pour cet utilisateur');
      return res.json({ backup: null });
    }

    res.json({ backup });
  });
});

// Route pour récupérer l'historique des sauvegardes
app.get('/api/backup/history', requireAuth, (req, res) => {
  console.log('📋 Récupération de l\'historique des sauvegardes pour l\'utilisateur:', req.user.id);

  getBackupHistory(req.user.id, (err, backups) => {
    if (err) {
      console.error('❌ Erreur lors de la récupération de l\'historique:', err);
      return res.status(500).json({ error: 'Erreur serveur' });
    }

    console.log(`📊 ${backups.length} sauvegardes trouvées pour l'historique`);
    res.json(backups);
  });
});

// Route pour supprimer une sauvegarde
app.delete('/api/backup/:id', requireAuth, (req, res) => {
  const backupId = parseInt(req.params.id);
  console.log('🗑️ Suppression de la sauvegarde:', backupId, 'pour l\'utilisateur:', req.user.id);

  if (!backupId || isNaN(backupId)) {
    return res.status(400).json({ error: 'ID de sauvegarde invalide' });
  }

  deleteBackupLog(backupId, req.user.id, (err, result) => {
    if (err) {
      console.error('❌ Erreur lors de la suppression:', err);
      return res.status(500).json({ error: err.message || 'Erreur serveur' });
    }

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Sauvegarde non trouvée' });
    }

    // Supprimer le fichier physique si il existe
    if (result.filePath) {
      try {
        const fullPath = path.join(__dirname, result.filePath);
        if (fs.existsSync(fullPath)) {
          fs.unlinkSync(fullPath);
          console.log('✅ Fichier de sauvegarde supprimé:', fullPath);
        }
      } catch (fileErr) {
        console.error('⚠️ Erreur lors de la suppression du fichier:', fileErr);
        // Ne pas faire échouer la requête si le fichier ne peut pas être supprimé
      }
    }

    console.log('✅ Sauvegarde supprimée avec succès');
    res.json({ success: true, message: 'Sauvegarde supprimée avec succès' });
  });
});

const PORT = process.env.PORT || 3333;
app.listen(PORT, () => console.log(`Server started on http://localhost:${PORT}`));