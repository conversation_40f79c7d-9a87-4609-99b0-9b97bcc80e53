// Script de test pour les modes de paiement
const API_BASE = 'http://localhost:3333/api';

// Fonction pour faire une requête avec authentification
async function apiRequest(endpoint, options = {}) {
  const token = 'test-token'; // Vous devrez remplacer par un vrai token
  
  const response = await fetch(`${API_BASE}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers
    }
  });
  
  if (!response.ok) {
    const error = await response.text();
    throw new Error(`HTTP ${response.status}: ${error}`);
  }
  
  return response.json();
}

// Test des modes de paiement
async function testPaymentMethods() {
  console.log('🧪 Test des modes de paiement...');
  
  try {
    // 1. Créer des modes de paiement par défaut
    const defaultMethods = [
      { name: '<PERSON><PERSON><PERSON>', requires_check_number: true },
      { name: 'Virement', requires_check_number: false },
      { name: '<PERSON>', requires_check_number: false },
      { name: '<PERSON> <PERSON>', requires_check_number: false },
      { name: '<PERSON><PERSON><PERSON><PERSON>', requires_check_number: false }
    ];
    
    console.log('📝 Création des modes de paiement par défaut...');
    for (const method of defaultMethods) {
      try {
        const result = await apiRequest('/payment-methods', {
          method: 'POST',
          body: JSON.stringify(method)
        });
        console.log(`✅ Mode de paiement créé: ${method.name} (ID: ${result.id})`);
      } catch (error) {
        if (error.message.includes('409')) {
          console.log(`⚠️  Mode de paiement déjà existant: ${method.name}`);
        } else {
          console.error(`❌ Erreur lors de la création de ${method.name}:`, error.message);
        }
      }
    }
    
    // 2. Lister tous les modes de paiement
    console.log('\n📋 Liste des modes de paiement:');
    const methods = await apiRequest('/payment-methods');
    methods.forEach(method => {
      console.log(`- ${method.name} ${method.requires_check_number ? '(nécessite n° chèque)' : ''}`);
    });
    
    // 3. Créer une transaction avec mode de paiement
    console.log('\n💰 Test de création de transaction avec mode de paiement...');
    const chequeMethod = methods.find(m => m.name === 'Chèque');
    if (chequeMethod) {
      try {
        const transaction = await apiRequest('/transactions', {
          method: 'POST',
          body: JSON.stringify({
            amount: 50.00,
            type: 'out',
            description: 'Test transaction avec chèque',
            payment_method_id: chequeMethod.id,
            check_number: '1234567',
            date_iso: new Date().toISOString()
          })
        });
        console.log(`✅ Transaction créée avec chèque (ID: ${transaction.id})`);
      } catch (error) {
        console.error('❌ Erreur lors de la création de transaction:', error.message);
      }
    }
    
    console.log('\n🎉 Tests terminés !');
    
  } catch (error) {
    console.error('❌ Erreur générale:', error.message);
  }
}

// Exécuter les tests si ce script est lancé directement
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testPaymentMethods();
} else {
  // Browser environment
  window.testPaymentMethods = testPaymentMethods;
  console.log('🔧 Fonctions de test disponibles: testPaymentMethods()');
}
